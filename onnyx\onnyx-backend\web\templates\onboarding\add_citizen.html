{% extends "base.html" %}

{% block title %}Add Citizen - ONNYX Onboarding{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-gray to-onyx-black relative overflow-hidden">
    <!-- Ambient Background Effects -->
    <div class="absolute inset-0 opacity-20">
        <div class="floating-particles"></div>
        <div class="data-streams"></div>
    </div>

    <!-- Header -->
    <section class="pt-24 pb-16 relative z-10">
        <div class="container-xl mx-auto px-6">
            <div class="text-center mb-16">
                <!-- ONNYX Logo -->
                <div class="mb-8 flex justify-center">
                    <div class="flex items-center justify-center group">
                        <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                             alt="ONNYX Logo"
                             class="onnyx-page-logo w-16 h-16 md:w-20 md:h-20 object-contain"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                        <!-- Fallback symbol only if image fails -->
                        <span class="text-6xl font-black text-cyber-cyan" style="display: none;">⬢</span>
                    </div>
                </div>

                <h1 class="text-4xl md:text-5xl font-orbitron font-bold text-text-primary mb-6">
                    <span class="hologram-text">👤 Add New Citizen</span>
                </h1>
                <p class="text-xl text-text-secondary max-w-2xl mx-auto leading-relaxed">
                    Register a new citizen with covenant identity and biblical tokenomics integration
                </p>
            </div>
        </div>
    </section>

    <!-- Form Section -->
    <section class="pb-16 relative z-10">
        <div class="container-xl mx-auto px-6">
            <div class="max-w-2xl mx-auto">
                <div class="glass-card-premium p-8 rounded-3xl shadow-2xl shadow-cyber-cyan/10">
                    <form method="POST" class="space-y-8" id="citizen-form">
                        <!-- Basic Information -->
                        <div class="space-y-6">
                            <div class="flex items-center space-x-3 mb-6">
                                <div class="w-8 h-8 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-xl flex items-center justify-center">
                                    <span class="text-onyx-black font-bold">1</span>
                                </div>
                                <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan">Basic Information</h3>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <label for="name" class="block text-sm font-semibold text-text-primary mb-3">Full Name *</label>
                                    <div class="relative">
                                        <input type="text" id="name" name="name" required
                                               class="w-full px-4 py-3 bg-glass-bg border border-glass-border rounded-xl text-text-primary placeholder-text-muted focus:border-cyber-cyan focus:ring-2 focus:ring-cyber-cyan/20 transition-all duration-300 hover:bg-glass-hover min-h-[44px]"
                                               placeholder="Enter full name">
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                            <svg class="w-5 h-5 text-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="mt-1 text-xs text-text-muted">Enter the citizen's full legal name</div>
                                </div>

                                <div>
                                    <label for="email" class="block text-sm font-semibold text-text-primary mb-3">Email Address *</label>
                                    <div class="relative">
                                        <input type="email" id="email" name="email" required
                                               class="w-full px-4 py-3 bg-glass-bg border border-glass-border rounded-xl text-text-primary placeholder-text-muted focus:border-cyber-cyan focus:ring-2 focus:ring-cyber-cyan/20 transition-all duration-300 hover:bg-glass-hover min-h-[44px]"
                                               placeholder="Enter email address">
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                            <svg class="w-5 h-5 text-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="mt-1 text-xs text-text-muted">Valid email address for account verification</div>
                                </div>
                            </div>
                        </div>

                        <!-- Covenant Identity -->
                        <div class="space-y-6">
                            <div class="flex items-center space-x-3 mb-6">
                                <div class="w-8 h-8 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-xl flex items-center justify-center">
                                    <span class="text-onyx-black font-bold">2</span>
                                </div>
                                <h3 class="text-2xl font-orbitron font-bold text-cyber-purple">Covenant Identity</h3>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <label for="covenant_tribe" class="block text-sm font-semibold text-text-primary mb-3">Covenant Tribe (Optional)</label>
                                    <div class="relative">
                                        <select id="covenant_tribe" name="covenant_tribe"
                                                class="w-full px-4 py-3 bg-glass-bg border border-glass-border rounded-xl text-text-primary focus:border-cyber-purple focus:ring-2 focus:ring-cyber-purple/20 transition-all duration-300 hover:bg-glass-hover min-h-[44px] appearance-none">
                                            <option value="">Select a tribe (optional)</option>
                                            {% for tribe in covenant_tribes %}
                                            <option value="{{ tribe }}">{{ tribe }}</option>
                                            {% endfor %}
                                        </select>
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                            <svg class="w-5 h-5 text-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <p class="text-xs text-text-muted mt-2">Choose if the citizen belongs to one of the 12 covenant tribes</p>
                                </div>

                                <div>
                                    <label for="witness_nation" class="block text-sm font-semibold text-text-primary mb-3">Witness Nation</label>
                                    <div class="relative">
                                        <select id="witness_nation" name="witness_nation"
                                                class="w-full px-4 py-3 bg-glass-bg border border-glass-border rounded-xl text-text-primary focus:border-cyber-purple focus:ring-2 focus:ring-cyber-purple/20 transition-all duration-300 hover:bg-glass-hover min-h-[44px] appearance-none">
                                            <option value="">Select a nation</option>
                                            {% for nation in witness_nations %}
                                            <option value="{{ nation }}">{{ nation }}</option>
                                            {% endfor %}
                                        </select>
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                            <svg class="w-5 h-5 text-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <p class="text-xs text-text-muted mt-2">Select the citizen's nation of residence</p>
                                </div>
                            </div>
                        </div>

                        <!-- Initial Balance -->
                        <div class="space-y-6">
                            <h3 class="text-2xl font-orbitron font-bold text-cyber-green mb-6">Initial Setup</h3>

                            <div>
                                <label for="initial_balance" class="block text-sm font-semibold text-white mb-2">Initial ONX Balance</label>
                                <input type="number" id="initial_balance" name="initial_balance" min="0" step="0.01" value="0"
                                       class="w-full px-4 py-3 bg-dark-800/50 border border-dark-600 rounded-lg text-white placeholder-text-secondary focus:border-cyber-green focus:ring-1 focus:ring-cyber-green transition-colors"
                                       placeholder="0.00">
                                <p class="text-xs text-text-secondary mt-2">Optional initial ONX token balance (default: 0)</p>
                            </div>
                        </div>

                        <!-- Biblical Tokenomics Info -->
                        <div class="bg-dark-800/30 border border-cyber-cyan/20 rounded-lg p-6">
                            <h4 class="text-lg font-orbitron font-bold text-cyber-cyan mb-4">📜 Biblical Tokenomics Integration</h4>
                            <div class="space-y-3 text-sm text-text-secondary">
                                <div class="flex items-start space-x-3">
                                    <span class="text-cyber-cyan">•</span>
                                    <span>Citizen will start with CIPP Tier 1 (Basic Verification)</span>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <span class="text-cyber-cyan">•</span>
                                    <span>Initial Etzem Score: 100 (Good Standing)</span>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <span class="text-cyber-cyan">•</span>
                                    <span>Mining Tier: Citizen (1× multiplier)</span>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <span class="text-cyber-cyan">•</span>
                                    <span>Eligible for Sabbath compliance bonuses</span>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <span class="text-cyber-cyan">•</span>
                                    <span>Can participate in gleaning pool distributions</span>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex flex-col sm:flex-row gap-4 pt-8">
                            <a href="{{ url_for('onboarding.dashboard') }}"
                               class="flex-1 px-6 py-3 bg-glass-bg border border-glass-border hover:bg-glass-hover text-text-primary font-semibold rounded-xl transition-all duration-300 text-center min-h-[44px] flex items-center justify-center">
                                <span class="mr-2">←</span>
                                Cancel
                            </a>
                            <button type="submit" id="submit-button"
                                    class="flex-1 px-6 py-3 bg-gradient-to-r from-cyber-cyan to-cyber-blue hover:from-cyber-blue hover:to-cyber-cyan text-onyx-black font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg shadow-cyber-cyan/20 hover:shadow-xl hover:shadow-cyber-cyan/30 min-h-[44px] flex items-center justify-center">
                                <span class="mr-2">👤</span>
                                <span>Add Citizen</span>
                            </button>
                        </div>

                        <!-- Form Validation Messages -->
                        <div id="form-messages" class="hidden mt-4">
                            <div class="glass-card p-4 border-cyber-red/30 bg-cyber-red/10">
                                <div class="flex items-center space-x-3">
                                    <svg class="w-5 h-5 text-cyber-red" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span class="text-cyber-red font-medium" id="form-message-text">Please correct the errors above</span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
// Enhanced form validation and user experience
document.addEventListener('DOMContentLoaded', function() {
    initializeForm();
    setupValidation();
    setupInteractiveElements();
});

function initializeForm() {
    const form = document.getElementById('citizen-form');
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');
    const submitButton = document.getElementById('submit-button');
    const formMessages = document.getElementById('form-messages');

    // Add floating particles effect
    addFloatingParticles();

    // Setup form validation
    setupRealTimeValidation();

    // Setup form submission
    form.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            showFormError('Please correct the errors above');
            return;
        }

        handleFormSubmission();
    });
}

function setupRealTimeValidation() {
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');

    // Name validation
    nameInput.addEventListener('input', function() {
        validateField(this, validateName(this.value));
    });

    nameInput.addEventListener('blur', function() {
        validateField(this, validateName(this.value));
    });

    // Email validation
    emailInput.addEventListener('input', function() {
        validateField(this, validateEmail(this.value));
    });

    emailInput.addEventListener('blur', function() {
        validateField(this, validateEmail(this.value));
    });
}

function validateName(name) {
    if (!name || name.trim().length < 2) {
        return { valid: false, message: 'Name must be at least 2 characters long' };
    }
    if (name.trim().length > 100) {
        return { valid: false, message: 'Name must be less than 100 characters' };
    }
    if (!/^[a-zA-Z\s\-'\.]+$/.test(name)) {
        return { valid: false, message: 'Name can only contain letters, spaces, hyphens, apostrophes, and periods' };
    }
    return { valid: true, message: 'Valid name' };
}

function validateEmail(email) {
    if (!email) {
        return { valid: false, message: 'Email is required' };
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        return { valid: false, message: 'Please enter a valid email address' };
    }
    return { valid: true, message: 'Valid email address' };
}

function validateField(field, validation) {
    const fieldContainer = field.parentElement;
    let errorElement = fieldContainer.querySelector('.field-error');

    if (validation.valid) {
        field.style.borderColor = 'var(--cyber-cyan)';
        field.style.boxShadow = '0 0 0 2px rgba(0, 255, 247, 0.2)';
        if (errorElement) {
            errorElement.remove();
        }
    } else {
        field.style.borderColor = 'var(--cyber-red)';
        field.style.boxShadow = '0 0 0 2px rgba(255, 51, 102, 0.2)';

        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error text-xs text-cyber-red mt-1';
            fieldContainer.appendChild(errorElement);
        }
        errorElement.textContent = validation.message;
    }
}

function validateForm() {
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');

    const nameValidation = validateName(nameInput.value);
    const emailValidation = validateEmail(emailInput.value);

    validateField(nameInput, nameValidation);
    validateField(emailInput, emailValidation);

    return nameValidation.valid && emailValidation.valid;
}

function handleFormSubmission() {
    const submitButton = document.getElementById('submit-button');
    const originalContent = submitButton.innerHTML;

    // Show loading state
    submitButton.innerHTML = `
        <div class="flex items-center justify-center">
            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-onyx-black mr-2"></div>
            <span>Adding Citizen...</span>
        </div>
    `;
    submitButton.disabled = true;

    // Hide any previous error messages
    hideFormError();
}

function showFormError(message) {
    const formMessages = document.getElementById('form-messages');
    const messageText = document.getElementById('form-message-text');

    messageText.textContent = message;
    formMessages.classList.remove('hidden');

    // Scroll to error message
    formMessages.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

function hideFormError() {
    const formMessages = document.getElementById('form-messages');
    formMessages.classList.add('hidden');
}

function setupInteractiveElements() {
    // Add hover effects to form elements
    const inputs = document.querySelectorAll('input, select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'translateY(-1px)';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'translateY(0)';
        });
    });

    // Add click effects to buttons
    const buttons = document.querySelectorAll('button, a');
    buttons.forEach(button => {
        button.addEventListener('mousedown', function() {
            this.style.transform = 'scale(0.98)';
        });

        button.addEventListener('mouseup', function() {
            this.style.transform = '';
        });
    });
}

function addFloatingParticles() {
    // Add subtle floating particles for enhanced visual appeal
    const container = document.querySelector('.glass-card-premium');
    if (container) {
        for (let i = 0; i < 3; i++) {
            const particle = document.createElement('div');
            particle.className = 'absolute w-1 h-1 bg-cyber-cyan rounded-full opacity-30 animate-ping';
            particle.style.top = Math.random() * 100 + '%';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 2 + 's';
            particle.style.animationDuration = (2 + Math.random() * 2) + 's';
            container.appendChild(particle);
        }
    }
}
</script>

<style>
.hologram-text {
    text-shadow: 0 0 20px rgba(0, 255, 247, 0.5);
}

.floating-particles::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(0, 255, 247, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(154, 0, 255, 0.3), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(0, 255, 247, 0.2), transparent);
    background-repeat: repeat;
    background-size: 100px 100px;
    animation: float 20s linear infinite;
}

@keyframes float {
    0% { transform: translateY(0px) translateX(0px); }
    33% { transform: translateY(-20px) translateX(10px); }
    66% { transform: translateY(-10px) translateX(-10px); }
    100% { transform: translateY(0px) translateX(0px); }
}

.data-streams::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(154, 0, 255, 0.1), transparent);
    background-size: 200% 100%;
    animation: dataFlow 3s infinite;
}

@keyframes dataFlow {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}
</style>
{% endblock %}
