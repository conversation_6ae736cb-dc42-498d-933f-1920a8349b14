"""
Onnyx Block Validator Module

This module provides functions for validating blocks before they are added to the blockchain.
It ensures that blocks have the correct structure, contain valid transactions, and follow
the consensus rules of the Onnyx blockchain.
"""

import sys
import os
import json
import time
import hashlib
import logging
from typing import Dict, List, Any, Tuple, Optional, Set, Union

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from shared.models.block import Block
from shared.models.transaction import Transaction
from validator import (
    validate_transaction,
    validate_transaction_safe,
    ValidationError
)

# Set up logging
logger = logging.getLogger("onnyx.vm.block_validator")

class BlockValidationError(Exception):
    """Exception raised for block validation errors."""
    pass

def validate_block_structure(block: Union[Dict[str, Any], Block]) -> bool:
    """
    Validates the structure of a block.

    Args:
        block: The block to validate (can be a dict or Block object)

    Returns:
        bool: True if the block structure is valid

    Raises:
        BlockValidationError: If the block structure is invalid
    """
    # Convert Block object to dict if needed
    if isinstance(block, Block):
        block_dict = {
            "block_height": block.block_height,
            "timestamp": block.timestamp,
            "transactions": block.transactions,
            "previous_hash": block.previous_hash,
            "block_hash": block.block_hash,
            "miner": block.miner,
            "difficulty": block.difficulty,
            "nonce": block.nonce,
            "merkle_root": block.merkle_root,
            "size": block.size,
            "version": block.version
        }
    elif isinstance(block, dict):
        block_dict = block
    else:
        logger.error("Block must be a dictionary or Block object")
        raise BlockValidationError("Block must be a dictionary or Block object")

    logger.debug(f"Validating structure of block: {block_dict.get('block_height', block_dict.get('index', 'unknown'))}")

    # Check required fields
    required_fields = ["timestamp", "transactions", "previous_hash"]

    # Check for either block_height or index
    if "block_height" not in block_dict and "index" not in block_dict:
        logger.error("Block missing required field: block_height or index")
        raise BlockValidationError("Block missing required field: block_height or index")

    # Check for either block_hash or hash
    if "block_hash" not in block_dict and "hash" not in block_dict:
        logger.error("Block missing required field: block_hash or hash")
        raise BlockValidationError("Block missing required field: block_hash or hash")

    for field in required_fields:
        if field not in block_dict:
            logger.error(f"Block missing required field: {field}")
            raise BlockValidationError(f"Block missing required field: {field}")

    # Get block height (index)
    block_height = block_dict.get("block_height", block_dict.get("index"))

    # Check field types
    if not isinstance(block_height, int):
        logger.error("Block height/index must be an integer")
        raise BlockValidationError("Block height/index must be an integer")

    if not isinstance(block_dict["timestamp"], int):
        logger.error("Block timestamp must be an integer")
        raise BlockValidationError("Block timestamp must be an integer")

    if not isinstance(block_dict["transactions"], list):
        logger.error("Block transactions must be a list")
        raise BlockValidationError("Block transactions must be a list")

    if not isinstance(block_dict["previous_hash"], str):
        logger.error("Block previous_hash must be a string")
        raise BlockValidationError("Block previous_hash must be a string")

    # Get block hash
    block_hash = block_dict.get("block_hash", block_dict.get("hash"))

    if not isinstance(block_hash, str):
        logger.error("Block hash must be a string")
        raise BlockValidationError("Block hash must be a string")

    # Check height is non-negative
    if block_height < 0:
        logger.error("Block height/index must be non-negative")
        raise BlockValidationError("Block height/index must be non-negative")

    # Check timestamp is reasonable
    current_time = int(time.time())
    if block_dict["timestamp"] > current_time + 3600:  # Allow up to 1 hour in the future
        logger.error(f"Block timestamp is too far in the future: {block_dict['timestamp']} > {current_time + 3600}")
        raise BlockValidationError("Block timestamp is too far in the future")

    # For non-genesis blocks, check previous_hash is not empty
    if block_height > 0 and not block_dict["previous_hash"]:
        logger.error("Non-genesis block must have a previous_hash")
        raise BlockValidationError("Non-genesis block must have a previous_hash")

    logger.info(f"Block structure validation passed for block: {block_height}")
    return True

def validate_block_hash(block: Union[Dict[str, Any], Block]) -> bool:
    """
    Validates that the block's hash matches its contents.

    Args:
        block: The block to validate (can be a dict or Block object)

    Returns:
        bool: True if the block hash is valid

    Raises:
        BlockValidationError: If the block hash is invalid
    """
    # Handle Block object
    if isinstance(block, Block):
        # For Block objects, we trust the hash calculation done at creation time
        # In a real implementation, we would recalculate the hash here
        logger.debug(f"Block hash validation skipped for Block object: {block.block_hash}")
        return True

    # Handle dictionary
    elif isinstance(block, dict):
        # Create a copy of the block without the hash field
        block_copy = block.copy()

        # Get the claimed hash (support both hash and block_hash fields)
        claimed_hash = block_copy.pop("hash", block_copy.pop("block_hash", None))

        if not claimed_hash:
            logger.error("Block is missing hash field")
            raise BlockValidationError("Block is missing hash field")

        # Calculate the hash of the block
        block_string = json.dumps(block_copy, sort_keys=True)
        calculated_hash = hashlib.sha256(block_string.encode()).hexdigest()

        # Compare the calculated hash with the claimed hash
        if calculated_hash != claimed_hash:
            logger.error(f"Block hash mismatch: claimed {claimed_hash}, calculated {calculated_hash}")
            raise BlockValidationError(f"Block hash mismatch: claimed {claimed_hash}, calculated {calculated_hash}")

        logger.info(f"Block hash validation passed: {claimed_hash}")
        return True

    else:
        logger.error("Block must be a dictionary or Block object")
        raise BlockValidationError("Block must be a dictionary or Block object")

def validate_block_transactions(block: Union[Dict[str, Any], Block]) -> bool:
    """
    Validates all transactions in a block.

    Args:
        block: The block to validate (can be a dict or Block object)

    Returns:
        bool: True if all transactions are valid

    Raises:
        BlockValidationError: If any transaction is invalid
    """
    # Get transactions based on block type
    if isinstance(block, Block):
        transaction_ids = block.transactions
        block_id = f"height {block.block_height}"
    else:
        transaction_ids = block.get("transactions", [])
        block_id = f"height {block.get('block_height', block.get('index', 'unknown'))}"

    logger.debug(f"Validating {len(transaction_ids)} transactions in block {block_id}")

    if not transaction_ids:
        # Empty blocks are allowed (e.g., for genesis block)
        logger.info(f"Block {block_id} has no transactions, skipping transaction validation")
        return True

    # Check for duplicate transaction IDs
    tx_ids = set()
    for tx_id in transaction_ids:
        if not tx_id:
            logger.error("Transaction ID is empty")
            raise BlockValidationError("Transaction ID is empty")

        if tx_id in tx_ids:
            logger.error(f"Duplicate transaction in block: {tx_id}")
            raise BlockValidationError(f"Duplicate transaction in block: {tx_id}")

        tx_ids.add(tx_id)

    # Validate each transaction
    for tx_id in transaction_ids:
        try:
            # Get the transaction from the database
            tx = Transaction.get_by_id(tx_id)

            if not tx:
                logger.error(f"Transaction {tx_id} not found in database")
                raise BlockValidationError(f"Transaction {tx_id} not found in database")

            # Validate the transaction
            validate_transaction(tx)
            logger.debug(f"Transaction {tx_id} is valid")

        except ValidationError as e:
            logger.error(f"Invalid transaction {tx_id}: {str(e)}")
            raise BlockValidationError(f"Invalid transaction {tx_id}: {str(e)}")
        except Exception as e:
            logger.error(f"Error validating transaction {tx_id}: {str(e)}")
            raise BlockValidationError(f"Error validating transaction {tx_id}: {str(e)}")

    logger.info(f"All {len(transaction_ids)} transactions in block {block_id} are valid")
    return True

def validate_block_miner_reward(block: Union[Dict[str, Any], Block], reward_amount: int = 50) -> bool:
    """
    Validates that the block includes a valid miner reward transaction.

    Args:
        block: The block to validate (can be a dict or Block object)
        reward_amount: The expected reward amount

    Returns:
        bool: True if the miner reward is valid

    Raises:
        BlockValidationError: If the miner reward is invalid
    """
    # Get block height and transactions based on block type
    if isinstance(block, Block):
        block_height = block.block_height
        transaction_ids = block.transactions
        block_id = f"height {block_height}"
    else:
        block_height = block.get("block_height", block.get("index", 0))
        transaction_ids = block.get("transactions", [])
        block_id = f"height {block_height}"

    # Skip for genesis block
    if block_height == 0:
        logger.info(f"Skipping miner reward validation for genesis block")
        return True

    logger.debug(f"Validating miner reward for block {block_id}")

    # Check if there are any transactions
    if not transaction_ids:
        logger.error(f"Block {block_id} has no transactions, missing miner reward")
        raise BlockValidationError(f"Block {block_id} has no transactions, missing miner reward")

    # Get the first transaction (coinbase)
    coinbase_tx_id = transaction_ids[0]
    coinbase_tx = Transaction.get_by_id(coinbase_tx_id)

    if not coinbase_tx:
        logger.error(f"Coinbase transaction {coinbase_tx_id} not found in database")
        raise BlockValidationError(f"Coinbase transaction {coinbase_tx_id} not found in database")

    # Check if it's a coinbase transaction
    if coinbase_tx.op != "OP_MINT" or coinbase_tx.data.get("from_id", None):
        logger.error(f"First transaction must be a coinbase (OP_MINT without 'from_id')")
        raise BlockValidationError(f"First transaction must be a coinbase (OP_MINT without 'from_id')")

    # Check the reward amount
    tx_reward = coinbase_tx.data.get("amount", 0)
    if tx_reward != reward_amount:
        logger.error(f"Invalid miner reward amount: {tx_reward}, expected {reward_amount}")
        raise BlockValidationError(f"Invalid miner reward amount: {tx_reward}, expected {reward_amount}")

    logger.info(f"Miner reward validation passed for block {block_id}")
    return True

def validate_block_against_chain(block: Union[Dict[str, Any], Block], previous_block: Union[Dict[str, Any], Block]) -> bool:
    """
    Validates a block against the previous block in the chain.

    Args:
        block: The block to validate (can be a dict or Block object)
        previous_block: The previous block in the chain (can be a dict or Block object)

    Returns:
        bool: True if the block is valid against the chain

    Raises:
        BlockValidationError: If the block is invalid against the chain
    """
    # Get block height, hash, and timestamp based on block type
    if isinstance(block, Block):
        block_height = block.block_height
        block_prev_hash = block.previous_hash
        block_timestamp = block.timestamp
        block_id = f"height {block_height}"
    else:
        block_height = block.get("block_height", block.get("index", 0))
        block_prev_hash = block.get("previous_hash", "")
        block_timestamp = block.get("timestamp", 0)
        block_id = f"height {block_height}"

    # Get previous block height, hash, and timestamp based on block type
    if isinstance(previous_block, Block):
        prev_height = previous_block.block_height
        prev_hash = previous_block.block_hash
        prev_timestamp = previous_block.timestamp
        prev_id = f"height {prev_height}"
    else:
        prev_height = previous_block.get("block_height", previous_block.get("index", 0))
        prev_hash = previous_block.get("block_hash", previous_block.get("hash", ""))
        prev_timestamp = previous_block.get("timestamp", 0)
        prev_id = f"height {prev_height}"

    logger.debug(f"Validating block {block_id} against previous block {prev_id}")

    # Check that the block's height is one more than the previous block
    if block_height != prev_height + 1:
        logger.error(f"Block height mismatch: {block_height} != {prev_height + 1}")
        raise BlockValidationError(f"Block height mismatch: {block_height} != {prev_height + 1}")

    # Check that the block's previous_hash matches the hash of the previous block
    if block_prev_hash != prev_hash:
        logger.error(f"Block previous_hash mismatch: {block_prev_hash} != {prev_hash}")
        raise BlockValidationError(f"Block previous_hash mismatch: {block_prev_hash} != {prev_hash}")

    # Check that the block's timestamp is after the previous block's timestamp
    if block_timestamp <= prev_timestamp:
        logger.error(f"Block timestamp must be after previous block: {block_timestamp} <= {prev_timestamp}")
        raise BlockValidationError(f"Block timestamp must be after previous block: {block_timestamp} <= {prev_timestamp}")

    logger.info(f"Block {block_id} is valid against previous block {prev_id}")
    return True

def validate_block_signature(block: Union[Dict[str, Any], Block]) -> bool:
    """
    Validates the signature of a block.

    Args:
        block: The block to validate (can be a dict or Block object)

    Returns:
        bool: True if the block signature is valid

    Raises:
        BlockValidationError: If the block signature is invalid
    """
    # Get block ID for logging
    if isinstance(block, Block):
        block_id = f"height {block.block_height}"
        miner = block.miner
    else:
        block_height = block.get("block_height", block.get("index", 0))
        block_id = f"height {block_height}"
        miner = block.get("miner", block.get("signed_by", "unknown"))

    logger.debug(f"Validating signature of block {block_id}")

    try:
        # Import here to avoid circular imports
        from blockchain.node.block_signer import block_signer

        # Verify the signature
        if isinstance(block, Block):
            # Convert Block to dict for signature verification
            block_dict = {
                "block_height": block.block_height,
                "timestamp": block.timestamp,
                "transactions": block.transactions,
                "previous_hash": block.previous_hash,
                "block_hash": block.block_hash,
                "miner": block.miner,
                "difficulty": block.difficulty,
                "nonce": block.nonce,
                "merkle_root": block.merkle_root,
                "size": block.size,
                "version": block.version
            }
            result = block_signer.verify_block_signature(block_dict)
        else:
            result = block_signer.verify_block_signature(block)

        if not result:
            logger.error(f"Invalid block signature from miner: {miner}")
            raise BlockValidationError(f"Invalid block signature from miner: {miner}")

        logger.info(f"Block {block_id} signature is valid")
        return True
    except ImportError:
        # If block_signer is not available, skip signature validation
        logger.warning(f"Block signer module not available, skipping signature validation for block {block_id}")
        return True
    except Exception as e:
        logger.error(f"Error validating block signature: {str(e)}")
        raise BlockValidationError(f"Error validating block signature: {str(e)}")

def validate_block(block: Union[Dict[str, Any], Block], previous_block: Optional[Union[Dict[str, Any], Block]] = None, check_miner_reward: bool = True, reward_amount: int = 50) -> bool:
    """
    Validates a block for addition to the blockchain.

    Args:
        block: The block to validate (can be a dict or Block object)
        previous_block: The previous block in the chain (None for genesis block)
        check_miner_reward: Whether to check for a valid miner reward
        reward_amount: The expected miner reward amount

    Returns:
        bool: True if the block is valid

    Raises:
        BlockValidationError: If the block is invalid
    """
    # Get block height for logging
    if isinstance(block, Block):
        block_height = block.block_height
        block_id = f"height {block_height}"
    else:
        block_height = block.get("block_height", block.get("index", 0))
        block_id = f"height {block_height}"

    logger.info(f"Starting validation of block {block_id}")

    # Validate block structure
    validate_block_structure(block)

    # Validate block hash
    validate_block_hash(block)

    # Validate block signature
    validate_block_signature(block)

    # Validate block transactions
    validate_block_transactions(block)

    # Validate miner reward if required
    if check_miner_reward and block_height > 0:
        validate_block_miner_reward(block, reward_amount)

    # Validate against previous block if provided
    if previous_block is not None:
        validate_block_against_chain(block, previous_block)
    elif block_height > 0:
        # Non-genesis block must have a previous block
        logger.error(f"Non-genesis block {block_id} requires previous_block for validation")
        raise BlockValidationError(f"Non-genesis block {block_id} requires previous_block for validation")

    logger.info(f"Block {block_id} is valid")
    return True

def validate_block_safe(block: Union[Dict[str, Any], Block], previous_block: Optional[Union[Dict[str, Any], Block]] = None, check_miner_reward: bool = True, reward_amount: int = 50) -> Tuple[bool, Optional[str]]:
    """
    Safely validates a block without raising exceptions.

    Args:
        block: The block to validate (can be a dict or Block object)
        previous_block: The previous block in the chain (None for genesis block)
        check_miner_reward: Whether to check for a valid miner reward
        reward_amount: The expected miner reward amount

    Returns:
        A tuple of (is_valid, error_message)
    """
    # Get block height for logging
    if isinstance(block, Block):
        block_height = block.block_height
        block_id = f"height {block_height}"
    else:
        block_height = block.get("block_height", block.get("index", 0))
        block_id = f"height {block_height}"

    logger.debug(f"Safely validating block {block_id}")

    try:
        validate_block(block, previous_block, check_miner_reward, reward_amount)
        logger.info(f"Block {block_id} is valid")
        return True, None
    except BlockValidationError as e:
        logger.warning(f"Block {block_id} validation failed: {str(e)}")
        return False, str(e)
    except Exception as e:
        logger.error(f"Unexpected error validating block {block_id}: {str(e)}")
        return False, f"Unexpected error: {str(e)}"

def validate_chain(chain: List[Union[Dict[str, Any], Block]], check_miner_reward: bool = True, reward_amount: int = 50) -> bool:
    """
    Validates an entire blockchain.

    Args:
        chain: The blockchain to validate (can be a list of dicts or Block objects)
        check_miner_reward: Whether to check for valid miner rewards
        reward_amount: The expected miner reward amount

    Returns:
        bool: True if the chain is valid

    Raises:
        BlockValidationError: If the chain is invalid
    """
    if not chain:
        logger.error("Chain is empty")
        raise BlockValidationError("Chain is empty")

    logger.info(f"Validating chain with {len(chain)} blocks")

    # Validate the genesis block
    validate_block(chain[0], None, check_miner_reward, reward_amount)
    logger.info("Genesis block is valid")

    # Validate each subsequent block against its predecessor
    for i in range(1, len(chain)):
        logger.debug(f"Validating block at position {i} in chain")
        validate_block(chain[i], chain[i-1], check_miner_reward, reward_amount)

    logger.info(f"Chain with {len(chain)} blocks is valid")
    return True
