"""
Governance Routes - Public Governance Interface
Handles transparency interfaces for:
- Public Council of 12 overview
- Active Voice Scrolls display
- Gate Keeper verification votes
- Governance process explanation
"""

from flask import Blueprint, render_template, request, jsonify
from shared.db.db import db
from shared.models.identity_verification import IdentityVerificationProposal
from shared.models.voice_scroll import VoiceScroll
import json
import logging

logger = logging.getLogger(__name__)

governance_bp = Blueprint('governance', __name__, url_prefix='/governance')

@governance_bp.route('/public')
def public_governance():
    """Public governance transparency page."""
    try:
        # Get Council of 12 Gate Keepers
        gate_keepers = db.query("""
            SELECT identity_id, name, metadata
            FROM identities 
            WHERE JSON_EXTRACT(metadata, '$.tribal_role') = 'Gate_Keeper'
            AND JSON_EXTRACT(metadata, '$.council_member') = 1
            ORDER BY name
        """)
        
        # Parse metadata for each Gate Keeper
        for gk in gate_keepers:
            if gk['metadata']:
                gk['metadata'] = json.loads(gk['metadata'])
        
        # Get active Voice Scrolls
        active_scrolls = VoiceScroll.find_by_status("active")
        
        # Get recent identity verification proposals
        pending_verifications = IdentityVerificationProposal.get_pending_proposals()
        
        # Get governance statistics
        stats = {
            'total_gate_keepers': len(gate_keepers),
            'active_proposals': len(active_scrolls),
            'pending_verifications': len(pending_verifications),
            'total_voice_scrolls': len(VoiceScroll.get_all())
        }
        
        return render_template('governance/public.html',
                             gate_keepers=gate_keepers,
                             active_scrolls=active_scrolls,
                             pending_verifications=pending_verifications,
                             stats=stats)
                             
    except Exception as e:
        logger.error(f"Error loading public governance: {e}")
        return render_template('error.html', 
                             error_message="Unable to load governance information"), 500

@governance_bp.route('/council')
def council_overview():
    """Detailed Council of 12 overview."""
    try:
        # Get all tribal representatives and Gate Keepers
        council_members = db.query("""
            SELECT identity_id, name, metadata
            FROM identities 
            WHERE (JSON_EXTRACT(metadata, '$.tribal_role') = 'Gate_Keeper'
                   OR JSON_EXTRACT(metadata, '$.tribal_role') = 'Elder')
            AND JSON_EXTRACT(metadata, '$.council_member') = 1
            ORDER BY JSON_EXTRACT(metadata, '$.tribe_name'), name
        """)
        
        # Parse metadata and group by tribe
        tribes = {}
        for member in council_members:
            if member['metadata']:
                metadata = json.loads(member['metadata'])
                tribe_name = metadata.get('tribe_name', 'Unknown')
                if tribe_name not in tribes:
                    tribes[tribe_name] = []
                member['metadata'] = metadata
                tribes[tribe_name].append(member)
        
        return render_template('governance/council.html',
                             council_members=council_members,
                             tribes=tribes)
                             
    except Exception as e:
        logger.error(f"Error loading council overview: {e}")
        return render_template('error.html', 
                             error_message="Unable to load council information"), 500

@governance_bp.route('/voice-scrolls')
def voice_scrolls():
    """Active Voice Scrolls display."""
    try:
        # Get all Voice Scrolls grouped by status
        all_scrolls = VoiceScroll.get_all()
        
        scrolls_by_status = {
            'active': [],
            'approved': [],
            'rejected': [],
            'expired': []
        }
        
        for scroll in all_scrolls:
            status = scroll.status.lower()
            if status in scrolls_by_status:
                scrolls_by_status[status].append(scroll)
        
        # Get identity verification scrolls specifically
        identity_scrolls = VoiceScroll.find_by_category("IDENTITY_VERIFICATION")
        
        return render_template('governance/voice_scrolls.html',
                             scrolls_by_status=scrolls_by_status,
                             identity_scrolls=identity_scrolls)
                             
    except Exception as e:
        logger.error(f"Error loading voice scrolls: {e}")
        return render_template('error.html', 
                             error_message="Unable to load voice scrolls"), 500

@governance_bp.route('/verification/<proposal_id>')
def verification_details(proposal_id):
    """Detailed view of an identity verification proposal."""
    try:
        # Get the Voice Scroll
        voice_scroll = VoiceScroll.find_by_id(proposal_id)
        if not voice_scroll:
            return render_template('error.html', 
                                 error_message="Verification proposal not found"), 404
        
        # Get the verification proposal details
        verification = db.query_one("""
            SELECT proposal_id, identity_id, tribe_code, applicant_data,
                   votes, status, created_at
            FROM identity_verification_proposals
            WHERE proposal_id = ?
        """, (proposal_id,))
        
        if verification:
            verification['applicant_data'] = json.loads(verification['applicant_data'])
            verification['votes'] = json.loads(verification['votes'])
        
        # Get Gate Keeper information for votes
        gate_keepers = db.query("""
            SELECT identity_id, name, metadata
            FROM identities 
            WHERE JSON_EXTRACT(metadata, '$.tribal_role') = 'Gate_Keeper'
            ORDER BY name
        """)
        
        for gk in gate_keepers:
            if gk['metadata']:
                gk['metadata'] = json.loads(gk['metadata'])
        
        return render_template('governance/verification_details.html',
                             voice_scroll=voice_scroll,
                             verification=verification,
                             gate_keepers=gate_keepers)
                             
    except Exception as e:
        logger.error(f"Error loading verification details: {e}")
        return render_template('error.html', 
                             error_message="Unable to load verification details"), 500

@governance_bp.route('/process')
def governance_process():
    """Governance process explanation."""
    return render_template('governance/process.html')

@governance_bp.route('/faq')
def governance_faq():
    """FAQ about biblical governance."""
    return render_template('governance/faq.html')

@governance_bp.route('/api/stats')
def api_governance_stats():
    """API endpoint for governance statistics."""
    try:
        # Get various governance statistics
        stats = {}
        
        # Gate Keeper stats
        gate_keepers = db.query("""
            SELECT COUNT(*) as count FROM identities 
            WHERE JSON_EXTRACT(metadata, '$.tribal_role') = 'Gate_Keeper'
        """)
        stats['gate_keepers'] = gate_keepers[0]['count'] if gate_keepers else 0
        
        # Voice Scrolls stats
        voice_scrolls = db.query("""
            SELECT status, COUNT(*) as count FROM voice_scrolls 
            GROUP BY status
        """)
        stats['voice_scrolls'] = {vs['status']: vs['count'] for vs in voice_scrolls}
        
        # Identity verification stats
        verifications = db.query("""
            SELECT status, COUNT(*) as count FROM identity_verification_proposals 
            GROUP BY status
        """)
        stats['verifications'] = {v['status']: v['count'] for v in verifications}
        
        # Tribal representation stats
        tribes = db.query("""
            SELECT JSON_EXTRACT(metadata, '$.tribe_name') as tribe, COUNT(*) as count
            FROM identities 
            WHERE JSON_EXTRACT(metadata, '$.tribal_role') IN ('Gate_Keeper', 'Elder')
            GROUP BY JSON_EXTRACT(metadata, '$.tribe_name')
        """)
        stats['tribal_representation'] = {t['tribe']: t['count'] for t in tribes if t['tribe']}
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"Error getting governance stats: {e}")
        return jsonify({'error': 'Failed to load governance statistics'}), 500

@governance_bp.route('/api/recent-activity')
def api_recent_activity():
    """API endpoint for recent governance activity."""
    try:
        # Get recent Voice Scrolls
        recent_scrolls = db.query("""
            SELECT scroll_id, title, category, status, created_at
            FROM voice_scrolls 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        # Get recent verification proposals
        recent_verifications = db.query("""
            SELECT proposal_id, identity_id, tribe_code, status, created_at
            FROM identity_verification_proposals 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        return jsonify({
            'success': True,
            'recent_scrolls': recent_scrolls,
            'recent_verifications': recent_verifications
        })
        
    except Exception as e:
        logger.error(f"Error getting recent activity: {e}")
        return jsonify({'error': 'Failed to load recent activity'}), 500
