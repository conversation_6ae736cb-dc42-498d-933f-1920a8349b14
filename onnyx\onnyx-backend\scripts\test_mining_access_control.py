#!/usr/bin/env python3
"""
Test Mining Access Control

This script tests the mining access control system to ensure only authorized users
can access mining controls.
"""

import os
import sys
import json
import logging

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db
from web.auth_decorators import get_role_permissions, has_permission, Permission, UserRole, determine_user_role

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_role_permissions():
    """Test role-based permission system"""
    logger.info("🧪 Testing Role-Based Permission System...")
    
    # Test each role's mining access
    test_cases = [
        (UserRole.SYSTEM_ADMIN, True, "System Admin should have mining access"),
        (UserRole.TRIBAL_ELDER, True, "Tribal Elder should have mining access"),
        (UserRole.ISRAELITE, True, "Israelite should have mining access"),
        (UserRole.GATE_KEEPER, False, "Gate Keeper should NOT have mining access"),
        (UserRole.WITNESS_NATION, <PERSON><PERSON><PERSON>, "Witness Nation should NOT have mining access"),
        (UserRole.OBSERVER, False, "Observer should NOT have mining access"),
        (UserRole.CITIZEN, False, "Citizen should NOT have mining access")
    ]
    
    for role, expected_access, description in test_cases:
        permissions = get_role_permissions(role)
        has_mining = Permission.MINING_CONTROL in permissions
        
        status = "✅" if has_mining == expected_access else "❌"
        logger.info(f"   {status} {role}: {has_mining} - {description}")
        
        if has_mining != expected_access:
            logger.error(f"❌ FAILED: {description}")
            return False
    
    logger.info("✅ Role permission tests passed")
    return True

def test_user_role_determination():
    """Test user role determination logic"""
    logger.info("🧪 Testing User Role Determination...")
    
    # Test cases for role determination
    test_identities = [
        # System Admin
        {
            'identity': {'nation_code': 'JU', 'verification_level': 2},
            'metadata': {'admin_privileges': True},
            'expected_role': UserRole.SYSTEM_ADMIN,
            'description': 'Admin with privileges'
        },
        # Tribal Elder
        {
            'identity': {'nation_code': 'JU', 'verification_level': 2},
            'metadata': {'role': 'tribal_elder'},
            'expected_role': UserRole.TRIBAL_ELDER,
            'description': 'Tribal Elder'
        },
        # Israelite (Judah)
        {
            'identity': {'nation_code': 'JU', 'verification_level': 1},
            'metadata': {},
            'expected_role': UserRole.ISRAELITE,
            'description': 'Israelite from Judah'
        },
        # Witness Nation (Egypt)
        {
            'identity': {'nation_code': 'EGY', 'verification_level': 1},
            'metadata': {},
            'expected_role': UserRole.WITNESS_NATION,
            'description': 'Witness Nation (Egypt)'
        },
        # Observer (unverified)
        {
            'identity': {'nation_code': 'JU', 'verification_level': 0},
            'metadata': {},
            'expected_role': UserRole.OBSERVER,
            'description': 'Unverified user'
        }
    ]
    
    for test_case in test_identities:
        identity = test_case['identity']
        metadata = test_case['metadata']
        expected_role = test_case['expected_role']
        description = test_case['description']
        
        determined_role = determine_user_role(identity, metadata)
        
        status = "✅" if determined_role == expected_role else "❌"
        logger.info(f"   {status} {description}: {determined_role} (expected: {expected_role})")
        
        if determined_role != expected_role:
            logger.error(f"❌ FAILED: {description}")
            return False
    
    logger.info("✅ User role determination tests passed")
    return True

def test_mining_access_scenarios():
    """Test specific mining access scenarios"""
    logger.info("🧪 Testing Mining Access Scenarios...")
    
    scenarios = [
        {
            'user_role': UserRole.SYSTEM_ADMIN,
            'has_selas': False,
            'should_have_access': True,
            'description': 'System Admin without Selas'
        },
        {
            'user_role': UserRole.TRIBAL_ELDER,
            'has_selas': False,
            'should_have_access': True,
            'description': 'Tribal Elder without Selas'
        },
        {
            'user_role': UserRole.ISRAELITE,
            'has_selas': True,
            'should_have_access': True,
            'description': 'Israelite with Selas'
        },
        {
            'user_role': UserRole.ISRAELITE,
            'has_selas': False,
            'should_have_access': True,  # Has permission, but would be blocked by Sela check
            'description': 'Israelite without Selas (has permission but needs Sela)'
        },
        {
            'user_role': UserRole.WITNESS_NATION,
            'has_selas': True,
            'should_have_access': False,
            'description': 'Witness Nation with Selas (still no access)'
        },
        {
            'user_role': UserRole.OBSERVER,
            'has_selas': False,
            'should_have_access': False,
            'description': 'Observer without Selas'
        }
    ]
    
    for scenario in scenarios:
        role = scenario['user_role']
        should_have_access = scenario['should_have_access']
        description = scenario['description']
        
        # Create mock user
        mock_user = {'role': role, 'identity_id': 'test_user'}
        
        # Check permission
        has_access = has_permission(mock_user, Permission.MINING_CONTROL)
        
        status = "✅" if has_access == should_have_access else "❌"
        logger.info(f"   {status} {description}: {has_access}")
        
        if has_access != should_have_access:
            logger.error(f"❌ FAILED: {description}")
            return False
    
    logger.info("✅ Mining access scenario tests passed")
    return True

def test_database_integration():
    """Test database integration for access control"""
    logger.info("🧪 Testing Database Integration...")
    
    try:
        # Test getting user identities
        identities = db.query("SELECT identity_id, nation_code, role_class FROM identities LIMIT 3")
        logger.info(f"   ✅ Found {len(identities)} test identities")
        
        # Test getting selas
        selas = db.query("SELECT sela_id, identity_id FROM selas LIMIT 3")
        logger.info(f"   ✅ Found {len(selas)} test selas")
        
        # Test role determination for real users
        for identity in identities:
            if isinstance(identity, dict):
                identity_data = identity
            else:
                identity_data = {
                    'identity_id': identity[0],
                    'nation_code': identity[1],
                    'role_class': identity[2] if len(identity) > 2 else 'citizen'
                }
            
            # Mock metadata
            metadata = {}
            role = determine_user_role(identity_data, metadata)
            logger.info(f"   ✅ Identity {identity_data['identity_id'][:12]}... -> {role}")
        
        logger.info("✅ Database integration tests passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database integration test failed: {e}")
        return False

def main():
    """Main test function"""
    logger.info("🔒 Testing Mining Access Control System")
    logger.info("=" * 60)
    
    tests = [
        ("Role Permissions", test_role_permissions),
        ("User Role Determination", test_user_role_determination),
        ("Mining Access Scenarios", test_mining_access_scenarios),
        ("Database Integration", test_database_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running {test_name}...")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} ERROR: {e}")
    
    logger.info("=" * 60)
    logger.info(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED!")
        logger.info("✅ Mining access control is working correctly")
        logger.info("🔒 Non-validators will no longer see mining controls")
        return 0
    else:
        logger.error("❌ Some tests failed")
        return 1

if __name__ == '__main__':
    exit(main())
