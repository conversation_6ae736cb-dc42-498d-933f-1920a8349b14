#!/usr/bin/env python3
"""
Test the Eden Mode create-identity endpoint
"""
import requests
import json

def test_create_identity():
    """Test the create-identity endpoint"""
    url = "http://127.0.0.1:5000/auth/eden-mode/create-identity"
    
    # Test data for Israelite user
    test_data = {
        'fullName': 'Test Israelite User',
        'email': '<EMAIL>',
        'hebrewName': 'Test Hebrew Name',
        'laborCategory': 'priest',
        'selaChoice': 'community',
        'selectedTribe': 'Judah',
        'selectedNation': 'ISR',
        'selectedNationName': 'Israel',
        'covenantPath': 'israelite',
        'gatekeeperVerified': False,
        'walletAddress': 'ONX1234567890ABCDEF',
        'developerBypass': True,
        'bypassType': 'developer',
        'skipGateKeeper': True
    }
    
    print("Testing Eden Mode create-identity endpoint...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data)
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        print(f"Response content: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Test passed!")
                return True
            else:
                print(f"❌ Test failed: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

if __name__ == "__main__":
    if test_create_identity():
        print("\n✅ All tests passed!")
    else:
        print("\n❌ Some tests failed!")
