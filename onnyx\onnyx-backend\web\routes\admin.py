"""
Admin Panel Routes

Administrative interface for system management and oversight.
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from web.auth_decorators import require_admin, require_auth, get_current_user
from shared.db.db import db
import json
import time
import logging

logger = logging.getLogger(__name__)

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/')
@require_admin
def dashboard():
    """Admin dashboard overview"""
    try:
        current_user = get_current_user()
        
        # Get system statistics
        stats = {
            'total_identities': db.query_one("SELECT COUNT(*) as count FROM identities")['count'],
            'active_identities': db.query_one("SELECT COUNT(*) as count FROM identities WHERE status = 'active'")['count'],
            'pending_verification': db.query_one("SELECT COUNT(*) as count FROM identities WHERE verification_level LIKE '%Pending%'")['count'],
            'total_selas': db.query_one("SELECT COUNT(*) as count FROM selas")['count'],
            'total_tokens': db.query_one("SELECT COUNT(*) as count FROM tokens")['count'],
            'total_pools': db.query_one("SELECT COUNT(*) as count FROM jubilee_pools")['count'],
            'total_deeds': db.query_one("SELECT COUNT(*) as count FROM deeds_ledger")['count'],
            'system_admins': db.query_one("SELECT COUNT(*) as count FROM identities WHERE role_class = 'system_admin'")['count']
        }
        
        # Get recent activities
        recent_identities = db.query("""
            SELECT identity_id, name, created_at, verification_level, status
            FROM identities 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        recent_deeds = db.query("""
            SELECT d.deed_id, d.deed_type, d.description, d.timestamp, i.name as identity_name
            FROM deeds_ledger d
            JOIN identities i ON d.identity_id = i.identity_id
            ORDER BY d.timestamp DESC
            LIMIT 10
        """)
        
        return render_template('admin/dashboard.html',
                             stats=stats,
                             recent_identities=recent_identities,
                             recent_deeds=recent_deeds,
                             current_user=current_user)
        
    except Exception as e:
        logger.error(f"Admin dashboard error: {e}")
        flash('Error loading admin dashboard', 'error')
        return redirect(url_for('dashboard.overview'))

@admin_bp.route('/users')
@require_admin
def user_management():
    """User management interface"""
    try:
        # Get all users with pagination
        page = request.args.get('page', 1, type=int)
        per_page = 20
        offset = (page - 1) * per_page
        
        users = db.query("""
            SELECT identity_id, name, email, verification_level, status, role_class, created_at
            FROM identities 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        """, (per_page, offset))
        
        total_users = db.query_one("SELECT COUNT(*) as count FROM identities")['count']
        
        return render_template('admin/users.html',
                             users=users,
                             page=page,
                             per_page=per_page,
                             total_users=total_users)
        
    except Exception as e:
        logger.error(f"User management error: {e}")
        flash('Error loading user management', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/system')
@require_admin
def system_management():
    """System configuration and management"""
    try:
        # Get system configuration
        system_info = {
            'database_tables': len(db.query("SELECT name FROM sqlite_master WHERE type='table'")),
            'total_records': sum([
                db.query_one(f"SELECT COUNT(*) as count FROM {table}")['count']
                for table in ['identities', 'selas', 'tokens', 'jubilee_pools', 'deeds_ledger']
            ]),
            'last_backup': 'Not configured',  # TODO: Implement backup system
            'system_version': '1.0.0-beta',
            'uptime': 'Unknown'  # TODO: Track system uptime
        }
        
        return render_template('admin/system.html',
                             system_info=system_info)
        
    except Exception as e:
        logger.error(f"System management error: {e}")
        flash('Error loading system management', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/api/user/<identity_id>/update', methods=['POST'])
@require_admin
def update_user(identity_id):
    """Update user information"""
    try:
        data = request.get_json()
        
        # Validate input
        allowed_fields = ['verification_level', 'status', 'role_class']
        updates = {k: v for k, v in data.items() if k in allowed_fields}
        
        if not updates:
            return jsonify({'error': 'No valid fields to update'}), 400
        
        # Build update query
        set_clause = ', '.join([f"{k} = ?" for k in updates.keys()])
        values = list(updates.values()) + [identity_id]
        
        db.execute(f"UPDATE identities SET {set_clause}, updated_at = ? WHERE identity_id = ?", 
                  values + [int(time.time())])
        
        # Log the admin action
        current_user = get_current_user()
        db.execute("""
            INSERT INTO deeds_ledger (deed_id, identity_id, deed_type, description, timestamp, metadata)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            f"ADMIN_UPDATE_{identity_id}_{int(time.time())}",
            current_user['identity_id'],
            'ADMIN_ACTION',
            f"Updated user {identity_id}",
            int(time.time()),
            json.dumps({'action': 'user_update', 'target': identity_id, 'changes': updates})
        ))
        
        return jsonify({'success': True, 'message': 'User updated successfully'})
        
    except Exception as e:
        logger.error(f"User update error: {e}")
        return jsonify({'error': 'Failed to update user'}), 500

@admin_bp.route('/api/system/stats')
@require_admin
def system_stats():
    """Get real-time system statistics"""
    try:
        stats = {
            'identities': {
                'total': db.query_one("SELECT COUNT(*) as count FROM identities")['count'],
                'active': db.query_one("SELECT COUNT(*) as count FROM identities WHERE status = 'active'")['count'],
                'pending': db.query_one("SELECT COUNT(*) as count FROM identities WHERE verification_level LIKE '%Pending%'")['count']
            },
            'selas': {
                'total': db.query_one("SELECT COUNT(*) as count FROM selas")['count'],
                'active': db.query_one("SELECT COUNT(*) as count FROM selas WHERE status = 'active'")['count']
            },
            'tokenomics': {
                'pools': db.query_one("SELECT COUNT(*) as count FROM jubilee_pools")['count'],
                'deeds': db.query_one("SELECT COUNT(*) as count FROM deeds_ledger")['count'],
                'tokens': db.query_one("SELECT COUNT(*) as count FROM tokens")['count']
            }
        }
        
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f"System stats error: {e}")
        return jsonify({'error': 'Failed to get system stats'}), 500
