"""
Manual User Onboarding Routes
Streamlined process for adding users, validators, and tribal elders
"""

import logging
import uuid
from datetime import datetime, timezone
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for

from shared.db.db import db
from shared.models.tokenomics import biblical_tokenomics
from web.auth_decorators import require_admin, require_permission, Permission, log_security_event, get_current_user

logger = logging.getLogger(__name__)

onboarding_bp = Blueprint('onboarding', __name__, url_prefix='/onboarding')

@onboarding_bp.route('/')
@require_permission(Permission.MANUAL_ONBOARDING)
def dashboard():
    """Onboarding dashboard for administrators. REQUIRES MANUAL_ONBOARDING PERMISSION."""
    try:
        # Get current platform statistics
        stats = {
            'identities': db.query_one("SELECT COUNT(*) as count FROM identities")['count'] or 0,
            'selas': db.query_one("SELECT COUNT(*) as count FROM selas WHERE status = 'active'")['count'] or 0,
            'tribal_elders': db.query_one("SELECT COUNT(*) as count FROM identities WHERE role = 'tribal_elder'")['count'] or 0,
            'citizens': db.query_one("SELECT COUNT(*) as count FROM identities WHERE role = 'citizen'")['count'] or 0
        }

        # Get recent onboarding activity
        recent_identities = db.query("""
            SELECT identity_id, name, role, covenant_tribe, created_at
            FROM identities
            ORDER BY created_at DESC
            LIMIT 10
        """)

        recent_selas = db.query("""
            SELECT sela_id, name, identity_id, mining_tier, created_at
            FROM selas
            WHERE status = 'active'
            ORDER BY created_at DESC
            LIMIT 10
        """)

        return render_template('onboarding/dashboard.html',
                             stats=stats,
                             recent_identities=recent_identities,
                             recent_selas=recent_selas)

    except Exception as e:
        logger.error(f"Error loading onboarding dashboard: {e}")
        flash(f"Error loading dashboard: {e}", "error")
        return render_template('onboarding/dashboard.html', stats={}, recent_identities=[], recent_selas=[])

@onboarding_bp.route('/citizen', methods=['GET', 'POST'])
@require_permission(Permission.MANUAL_ONBOARDING)
def add_citizen():
    """Add a new citizen identity. REQUIRES MANUAL_ONBOARDING PERMISSION."""
    if request.method == 'POST':
        try:
            # Get form data
            name = request.form.get('name', '').strip()
            email = request.form.get('email', '').strip()
            covenant_tribe = request.form.get('covenant_tribe', '').strip()
            witness_nation = request.form.get('witness_nation', '').strip()
            initial_balance = float(request.form.get('initial_balance', 0))

            # Validation
            if not name:
                flash("Name is required", "error")
                return render_template('onboarding/add_citizen.html')

            if not email:
                flash("Email is required", "error")
                return render_template('onboarding/add_citizen.html')

            # Generate identity ID
            identity_id = f"citizen_{uuid.uuid4().hex[:8]}"
            timestamp = int(datetime.now(timezone.utc).timestamp())

            # Create identity
            db.execute("""
                INSERT INTO identities (
                    identity_id, name, email, role, covenant_tribe, witness_nation,
                    cipp_tier, etzem_score, created_at, updated_at
                ) VALUES (?, ?, ?, 'citizen', ?, ?, 1, 100, ?, ?)
            """, (identity_id, name, email, covenant_tribe, witness_nation, timestamp, timestamp))

            # Create initial token balance if specified
            if initial_balance > 0:
                db.execute("""
                    INSERT INTO token_balances (identity_id, token_id, balance, last_updated)
                    VALUES (?, 'ONX', ?, ?)
                """, (identity_id, initial_balance, timestamp))

            # Record onboarding deed
            biblical_tokenomics.record_deed(identity_id, "CITIZEN_ONBOARDING", 10.0,
                                          f"Manual citizen onboarding: {name}")

            flash(f"✅ Citizen {name} successfully onboarded with ID: {identity_id}", "success")
            return redirect(url_for('onboarding.dashboard'))

        except Exception as e:
            logger.error(f"Error adding citizen: {e}")
            flash(f"Error adding citizen: {e}", "error")

    # Get available tribes and nations for form
    covenant_tribes = [
        'Reuben', 'Simeon', 'Levi', 'Judah', 'Dan', 'Naphtali',
        'Gad', 'Asher', 'Issachar', 'Zebulun', 'Joseph', 'Benjamin'
    ]

    witness_nations = [
        'United States', 'Canada', 'United Kingdom', 'Australia', 'Germany',
        'France', 'Netherlands', 'Sweden', 'Norway', 'Switzerland'
    ]

    return render_template('onboarding/add_citizen.html',
                         covenant_tribes=covenant_tribes,
                         witness_nations=witness_nations)

@onboarding_bp.route('/validator', methods=['GET', 'POST'])
@require_permission(Permission.MANUAL_ONBOARDING)
def add_validator():
    """Add a new business validator (Sela). REQUIRES MANUAL_ONBOARDING PERMISSION."""
    if request.method == 'POST':
        try:
            # Get form data
            business_name = request.form.get('business_name', '').strip()
            identity_id = request.form.get('identity_id', '').strip()
            mining_tier = request.form.get('mining_tier', 'basic')
            business_type = request.form.get('business_type', '').strip()
            description = request.form.get('description', '').strip()

            # Validation
            if not business_name:
                flash("Business name is required", "error")
                return render_template('onboarding/add_validator.html')

            if not identity_id:
                flash("Identity ID is required", "error")
                return render_template('onboarding/add_validator.html')

            # Verify identity exists
            identity = db.query_one("SELECT * FROM identities WHERE identity_id = ?", (identity_id,))
            if not identity:
                flash("Identity ID not found", "error")
                return render_template('onboarding/add_validator.html')

            # Generate Sela ID
            sela_id = f"sela_{uuid.uuid4().hex[:8]}"
            timestamp = int(datetime.now(timezone.utc).timestamp())

            # Create Sela (using correct schema with 'name' instead of 'business_name')
            db.execute("""
                INSERT INTO selas (
                    sela_id, name, identity_id, mining_tier, category,
                    description, status, created_at, metadata
                ) VALUES (?, ?, ?, ?, ?, ?, 'active', ?, ?)
            """, (sela_id, business_name, identity_id, mining_tier, business_type, description, timestamp, '{}'))

            # Record validator onboarding deed
            biblical_tokenomics.record_deed(identity_id, "VALIDATOR_ONBOARDING", 50.0,
                                          f"Business validator onboarding: {business_name}")

            flash(f"✅ Validator {business_name} successfully onboarded with ID: {sela_id}", "success")
            return redirect(url_for('onboarding.dashboard'))

        except Exception as e:
            logger.error(f"Error adding validator: {e}")
            flash(f"Error adding validator: {e}", "error")

    # Get available identities for form
    available_identities = db.query("""
        SELECT identity_id, name, email
        FROM identities
        WHERE identity_id NOT IN (SELECT identity_id FROM selas WHERE status = 'active')
        ORDER BY name
    """)

    mining_tiers = [
        ('basic', 'Basic (2.5× multiplier)'),
        ('dual', 'Dual Sela (5.5× multiplier)'),
        ('triple', 'Triple Sela (7.0× multiplier)'),
        ('pro', 'Pro Validator (9.0× multiplier)')
    ]

    return render_template('onboarding/add_validator.html',
                         available_identities=available_identities,
                         mining_tiers=mining_tiers)

@onboarding_bp.route('/tribal-elder', methods=['GET', 'POST'])
@require_permission(Permission.MANUAL_ONBOARDING)
def add_tribal_elder():
    """Add a new tribal elder with governance privileges. REQUIRES MANUAL_ONBOARDING PERMISSION."""
    if request.method == 'POST':
        try:
            # Get form data
            name = request.form.get('name', '').strip()
            email = request.form.get('email', '').strip()
            covenant_tribe = request.form.get('covenant_tribe', '').strip()
            governance_role = request.form.get('governance_role', '').strip()
            initial_balance = float(request.form.get('initial_balance', 1000))

            # Validation
            if not name or not email or not covenant_tribe:
                flash("Name, email, and covenant tribe are required", "error")
                return render_template('onboarding/add_tribal_elder.html')

            # Generate identity ID
            identity_id = f"elder_{uuid.uuid4().hex[:8]}"
            timestamp = int(datetime.now(timezone.utc).timestamp())

            # Create tribal elder identity
            db.execute("""
                INSERT INTO identities (
                    identity_id, name, email, role, covenant_tribe, governance_role,
                    cipp_tier, etzem_score, created_at, updated_at
                ) VALUES (?, ?, ?, 'tribal_elder', ?, ?, 3, 500, ?, ?)
            """, (identity_id, name, email, covenant_tribe, governance_role, timestamp, timestamp))

            # Create initial token balance
            db.execute("""
                INSERT INTO token_balances (identity_id, token_id, balance, last_updated)
                VALUES (?, 'ONX', ?, ?)
            """, (identity_id, initial_balance, timestamp))

            # Record elder onboarding deed
            biblical_tokenomics.record_deed(identity_id, "ELDER_ONBOARDING", 100.0,
                                          f"Tribal elder onboarding: {name} of {covenant_tribe}")

            flash(f"✅ Tribal Elder {name} successfully onboarded with ID: {identity_id}", "success")
            return redirect(url_for('onboarding.dashboard'))

        except Exception as e:
            logger.error(f"Error adding tribal elder: {e}")
            flash(f"Error adding tribal elder: {e}", "error")

    covenant_tribes = [
        'Reuben', 'Simeon', 'Levi', 'Judah', 'Dan', 'Naphtali',
        'Gad', 'Asher', 'Issachar', 'Zebulun', 'Joseph', 'Benjamin'
    ]

    governance_roles = [
        'High Priest', 'Judge', 'Prophet', 'Teacher', 'Guardian', 'Scribe'
    ]

    return render_template('onboarding/add_tribal_elder.html',
                         covenant_tribes=covenant_tribes,
                         governance_roles=governance_roles)

@onboarding_bp.route('/api/search-identity')
def search_identity():
    """API endpoint to search for identities."""
    try:
        query = request.args.get('q', '').strip()
        if len(query) < 2:
            return jsonify([])

        identities = db.query("""
            SELECT identity_id, name, email, role, covenant_tribe
            FROM identities
            WHERE name LIKE ? OR email LIKE ? OR identity_id LIKE ?
            ORDER BY name
            LIMIT 10
        """, (f"%{query}%", f"%{query}%", f"%{query}%"))

        return jsonify(identities)

    except Exception as e:
        logger.error(f"Error searching identities: {e}")
        return jsonify([])

@onboarding_bp.route('/bulk-import', methods=['GET', 'POST'])
def bulk_import():
    """Bulk import users from CSV or JSON."""
    if request.method == 'POST':
        try:
            # Handle file upload and bulk import
            # This would be implemented based on specific requirements
            flash("Bulk import functionality coming soon", "info")
            return redirect(url_for('onboarding.dashboard'))
        except Exception as e:
            logger.error(f"Error in bulk import: {e}")
            flash(f"Bulk import error: {e}", "error")

    return render_template('onboarding/bulk_import.html')
