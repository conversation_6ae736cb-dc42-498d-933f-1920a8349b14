#!/usr/bin/env python3
"""
ONNYX Platform User Experience Test
Tests the complete user journey for both Israelite and Witness Nation registration flows.
"""

import time
import sys
import os
import subprocess
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.support.ui import Select
from webdriver_manager.chrome import ChromeDriverManager
import json

class ONNYXUserExperienceTest:
    def __init__(self):        self.driver = None
        self.wait = None
        self.base_url = "http://localhost:5000"
        self.test_results = []
        
    def setup_driver(self):
        """Setup Chrome WebDriver with appropriate options"""
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        # chrome_options.add_argument("--headless")  # Uncomment for headless mode
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 15)
            print("✅ Chrome WebDriver initialized successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to initialize WebDriver: {e}")
            print("Please ensure Chrome is installed")
            return False
    
    def log_test_result(self, step, success, message, error=None):
        """Log test results for reporting"""
        result = {
            "step": step,
            "success": success,
            "message": message,
            "error": str(error) if error else None,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {step}: {message}")
        if error:
            print(f"   Error: {error}")
    
    def wait_for_element(self, by, value, timeout=15):
        """Wait for element to be present and clickable"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            return element
        except TimeoutException:
            print(f"❌ Timeout waiting for element: {by}={value}")
            return None
    
    def safe_click(self, element, description="element"):
        """Safely click an element with error handling"""
        try:
            # Scroll element into view
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.5)
            
            # Try regular click first
            element.click()
            return True
        except ElementClickInterceptedException:
            # If regular click fails, try JavaScript click
            try:
                self.driver.execute_script("arguments[0].click();", element)
                return True
            except Exception as e:
                print(f"❌ Failed to click {description}: {e}")
                return False
        except Exception as e:
            print(f"❌ Failed to click {description}: {e}")
            return False
    
    def check_server_running(self):
        """Check if the Flask server is running"""
        try:
            self.driver.get(self.base_url)
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            self.log_test_result("Server Check", True, "ONNYX platform is accessible")
            return True
        except Exception as e:
            self.log_test_result("Server Check", False, "Cannot access ONNYX platform", e)
            return False
    
    def navigate_to_eden_mode(self):
        """Navigate to Eden Mode registration"""
        try:
            # Go to login page
            self.driver.get(f"{self.base_url}/auth/login")
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            # Look for Eden Mode link
            eden_mode_link = self.wait_for_element(By.PARTIAL_LINK_TEXT, "Eden Mode")
            if not eden_mode_link:
                # Try alternative selectors
                eden_mode_link = self.wait_for_element(By.XPATH, "//a[contains(text(), 'Eden Mode')]")
            
            if eden_mode_link:
                self.safe_click(eden_mode_link, "Eden Mode link")
                self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                
                # Check if we're on the Eden Mode page
                if "eden-mode" in self.driver.current_url:
                    self.log_test_result("Navigation", True, "Successfully navigated to Eden Mode")
                    return True
                else:
                    self.log_test_result("Navigation", False, "Not on Eden Mode page after clicking link")
                    return False
            else:
                # Try direct navigation
                self.driver.get(f"{self.base_url}/auth/eden-mode")
                self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                self.log_test_result("Navigation", True, "Direct navigation to Eden Mode successful")
                return True
                
        except Exception as e:
            self.log_test_result("Navigation", False, "Failed to navigate to Eden Mode", e)
            return False
    
    def test_israelite_registration(self):
        """Test complete Israelite registration flow"""
        print("\n🔹 Testing Israelite Registration Flow")
        
        try:
            # Step 1: Select Israelite path
            israelite_button = self.wait_for_element(By.XPATH, "//button[contains(text(), 'Begin Israelite Journey')]")
            if not israelite_button:
                israelite_button = self.wait_for_element(By.XPATH, "//button[contains(@onclick, 'israelite')]")
            
            if israelite_button:
                self.safe_click(israelite_button, "Israelite Journey button")
                time.sleep(2)
                
                # Wait for page transition
                self.wait.until(lambda driver: "israelite-step2" in driver.current_url)
                self.log_test_result("Israelite Step 1", True, "Successfully selected Israelite path")
            else:
                self.log_test_result("Israelite Step 1", False, "Could not find Israelite button")
                return False
            
            # Step 2: Select tribe
            tribe_buttons = self.driver.find_elements(By.XPATH, "//button[contains(@onclick, 'selectTribe')]")
            if tribe_buttons:
                # Select first available tribe
                self.safe_click(tribe_buttons[0], "Tribe selection")
                time.sleep(1)
                
                # Click proceed button
                proceed_button = self.wait_for_element(By.XPATH, "//button[contains(text(), 'Proceed')]")
                if proceed_button:
                    self.safe_click(proceed_button, "Proceed button")
                    time.sleep(2)
                    self.log_test_result("Israelite Step 2", True, "Successfully selected tribe")
                else:
                    self.log_test_result("Israelite Step 2", False, "Could not find proceed button")
                    return False
            else:
                self.log_test_result("Israelite Step 2", False, "Could not find tribe selection buttons")
                return False
            
            # Step 3: Use developer bypass
            bypass_button = self.wait_for_element(By.XPATH, "//button[contains(text(), 'Developer Bypass')]")
            if bypass_button:
                self.safe_click(bypass_button, "Developer Bypass button")
                time.sleep(1)
                
                # Enter bypass password
                password_input = self.wait_for_element(By.ID, "bypassPassword")
                if password_input:
                    password_input.send_keys("Israel United In Christ")
                    
                    # Click validate button
                    validate_button = self.wait_for_element(By.XPATH, "//button[contains(text(), 'Bypass Gate Keeper')]")
                    if validate_button:
                        self.safe_click(validate_button, "Validate bypass button")
                        time.sleep(3)
                        
                        # Wait for success modal and proceed
                        proceed_modal_button = self.wait_for_element(By.XPATH, "//button[contains(text(), 'Continue to Inscription')]")
                        if proceed_modal_button:
                            self.safe_click(proceed_modal_button, "Continue to Inscription button")
                            time.sleep(2)
                            self.log_test_result("Israelite Step 3", True, "Successfully used developer bypass")
                        else:
                            self.log_test_result("Israelite Step 3", False, "Could not find continue button in modal")
                            return False
                    else:
                        self.log_test_result("Israelite Step 3", False, "Could not find validate button")
                        return False
                else:
                    self.log_test_result("Israelite Step 3", False, "Could not find password input")
                    return False
            else:
                self.log_test_result("Israelite Step 3", False, "Could not find developer bypass button")
                return False
            
            # Step 4: Complete inscription
            # Fill out the inscription form
            if "israelite-step5" in self.driver.current_url:
                # Fill identity name
                identity_input = self.wait_for_element(By.ID, "identityName")
                if identity_input:
                    identity_input.send_keys("Test Israelite User")
                
                # Fill email
                email_input = self.wait_for_element(By.ID, "email")
                if email_input:
                    email_input.send_keys("<EMAIL>")
                
                # Submit form
                submit_button = self.wait_for_element(By.XPATH, "//button[contains(text(), 'Complete Registration')]")
                if submit_button:
                    self.safe_click(submit_button, "Complete Registration button")
                    time.sleep(3)
                    self.log_test_result("Israelite Step 4", True, "Successfully completed Israelite registration")
                    return True
                else:
                    self.log_test_result("Israelite Step 4", False, "Could not find complete registration button")
                    return False
            else:
                self.log_test_result("Israelite Step 4", False, "Not on inscription page")
                return False
                
        except Exception as e:
            self.log_test_result("Israelite Registration", False, "Unexpected error during Israelite registration", e)
            return False
    
    def test_witness_nation_registration(self):
        """Test complete Witness Nation registration flow"""
        print("\n🔹 Testing Witness Nation Registration Flow")
        
        try:
            # Navigate back to Eden Mode step 1
            self.driver.get(f"{self.base_url}/auth/eden-mode")
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            time.sleep(2)
            
            # Step 1: Select Witness Nation path
            witness_button = self.wait_for_element(By.XPATH, "//button[contains(text(), 'Begin Witness Journey')]")
            if not witness_button:
                witness_button = self.wait_for_element(By.XPATH, "//button[contains(@onclick, 'witness')]")
            
            if witness_button:
                self.safe_click(witness_button, "Witness Journey button")
                time.sleep(2)
                
                # Wait for page transition
                self.wait.until(lambda driver: "witness-step2" in driver.current_url)
                self.log_test_result("Witness Step 1", True, "Successfully selected Witness Nation path")
            else:
                self.log_test_result("Witness Step 1", False, "Could not find Witness Nation button")
                return False
            
            # Step 2: Select nation
            nation_buttons = self.driver.find_elements(By.XPATH, "//button[contains(@onclick, 'selectNation')]")
            if nation_buttons:
                # Select first available nation
                self.safe_click(nation_buttons[0], "Nation selection")
                time.sleep(1)
                
                # Click proceed button
                proceed_button = self.wait_for_element(By.XPATH, "//button[contains(text(), 'Proceed')]")
                if proceed_button:
                    self.safe_click(proceed_button, "Proceed button")
                    time.sleep(2)
                    self.log_test_result("Witness Step 2", True, "Successfully selected nation")
                else:
                    self.log_test_result("Witness Step 2", False, "Could not find proceed button")
                    return False
            else:
                self.log_test_result("Witness Step 2", False, "Could not find nation selection buttons")
                return False
            
            # Step 3: Complete registration (witness nations skip verification)
            if "witness-step" in self.driver.current_url:
                # Fill out any required forms
                identity_input = self.wait_for_element(By.ID, "identityName")
                if identity_input:
                    identity_input.send_keys("Test Witness User")
                
                email_input = self.wait_for_element(By.ID, "email")
                if email_input:
                    email_input.send_keys("<EMAIL>")
                
                # Submit form
                submit_button = self.wait_for_element(By.XPATH, "//button[contains(text(), 'Complete Registration')]")
                if submit_button:
                    self.safe_click(submit_button, "Complete Registration button")
                    time.sleep(3)
                    self.log_test_result("Witness Step 3", True, "Successfully completed Witness Nation registration")
                    return True
                else:
                    self.log_test_result("Witness Step 3", False, "Could not find complete registration button")
                    return False
            else:
                self.log_test_result("Witness Step 3", False, "Not on witness registration page")
                return False
                
        except Exception as e:
            self.log_test_result("Witness Registration", False, "Unexpected error during Witness Nation registration", e)
            return False
    
    def run_tests(self):
        """Run all user experience tests"""
        print("🚀 Starting ONNYX User Experience Tests")
        print("=" * 50)
        
        if not self.setup_driver():
            return False
        
        try:
            # Check server
            if not self.check_server_running():
                return False
            
            # Navigate to Eden Mode
            if not self.navigate_to_eden_mode():
                return False
            
            # Test Israelite registration
            self.test_israelite_registration()
            
            # Test Witness Nation registration
            self.test_witness_nation_registration()
            
            # Generate report
            self.generate_report()
            
            return True
            
        except Exception as e:
            print(f"❌ Unexpected error during testing: {e}")
            return False
        finally:
            if self.driver:
                self.driver.quit()
                print("🔚 WebDriver closed")
    
    def generate_report(self):
        """Generate test report"""
        print("\n📊 Test Results Summary")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['step']}: {result['message']}")
                    if result["error"]:
                        print(f"    Error: {result['error']}")
        
        # Save detailed report
        with open("test_report.json", "w") as f:
            json.dump(self.test_results, f, indent=2)
        print("\n📄 Detailed report saved to test_report.json")

def main():
    """Main function to run the tests"""
    print("ONNYX Platform User Experience Test Suite")
    print("This will test the complete user registration flows")
    print("=" * 60)
    
    # Check if server is running
    try:
        import requests
        response = requests.get("http://localhost:5000", timeout=5)
        if response.status_code != 200:
            print("❌ ONNYX server is not responding properly")
            return False
    except:
        print("❌ ONNYX server is not running on localhost:5000")
        print("Please start the server with: python app.py")
        return False
    
    # Run tests
    tester = ONNYXUserExperienceTest()
    success = tester.run_tests()
    
    if success:
        print("\n✅ User experience testing completed")
    else:
        print("\n❌ User experience testing failed")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
