"""
Tribes Routes - Educational Content and Tribal Selection System
Handles the comprehensive tribal selection system including:
- 12 Tribes of Israel overview
- 12 Dukes of Edom
- 12 Princes of Ishmael  
- Hamitic and Japhethic nations from Genesis genealogies
"""

from flask import Blueprint, render_template, request, jsonify
from shared.db.db import db
import logging

logger = logging.getLogger(__name__)

tribes_bp = Blueprint('tribes', __name__, url_prefix='/tribes')

@tribes_bp.route('/overview')
def overview():
    """Comprehensive tribal overview page."""
    try:
        # Get the 12 Tribes of Israel (covenant nations)
        covenant_nations = db.query("""
            SELECT nation_code, nation_name, tribe_name, flag_symbol, description
            FROM biblical_nations 
            WHERE nation_type = 'covenant' 
            ORDER BY nation_name
        """)
        
        # Get the 12 Dukes of Edom
        edom_dukes = db.query("""
            SELECT nation_code, nation_name, tribe_name, flag_symbol, description
            FROM biblical_nations 
            WHERE ancestral_group = 'Edom' 
            ORDER BY nation_name
        """)
        
        # Get the 12 Princes of <PERSON><PERSON><PERSON>
        ishmael_princes = db.query("""
            SELECT nation_code, nation_name, tribe_name, flag_symbol, description
            FROM biblical_nations 
            WHERE ancestral_group = 'Ishmael' 
            ORDER BY nation_name
        """)
        
        # Get Hamitic Nations
        hamitic_nations = db.query("""
            SELECT nation_code, nation_name, tribe_name, flag_symbol, description
            FROM biblical_nations 
            WHERE ancestral_group = 'Hamitic Nations' 
            ORDER BY nation_name
        """)
        
        # Get Japhethic Nations (if any exist)
        japhethic_nations = db.query("""
            SELECT nation_code, nation_name, tribe_name, flag_symbol, description
            FROM biblical_nations 
            WHERE ancestral_group = 'Japhethic Nations' 
            ORDER BY nation_name
        """)
        
        return render_template('tribes/overview.html',
                             covenant_nations=covenant_nations,
                             edom_dukes=edom_dukes,
                             ishmael_princes=ishmael_princes,
                             hamitic_nations=hamitic_nations,
                             japhethic_nations=japhethic_nations)
                             
    except Exception as e:
        logger.error(f"Error loading tribes overview: {e}")
        return render_template('error.html', 
                             error_message="Unable to load tribal information"), 500

@tribes_bp.route('/israel')
def israel_tribes():
    """Detailed page for the 12 Tribes of Israel."""
    try:
        covenant_nations = db.query("""
            SELECT nation_code, nation_name, tribe_name, flag_symbol, description,
                   biblical_reference, tribal_calling
            FROM biblical_nations 
            WHERE nation_type = 'covenant' 
            ORDER BY 
                CASE nation_name
                    WHEN 'Judah' THEN 1
                    WHEN 'Benjamin' THEN 2
                    WHEN 'Levi' THEN 3
                    WHEN 'Reuben' THEN 4
                    WHEN 'Simeon' THEN 5
                    WHEN 'Gad' THEN 6
                    WHEN 'Asher' THEN 7
                    WHEN 'Naphtali' THEN 8
                    WHEN 'Dan' THEN 9
                    WHEN 'Issachar' THEN 10
                    WHEN 'Zebulun' THEN 11
                    WHEN 'Joseph' THEN 12
                    ELSE 13
                END
        """)
        
        return render_template('tribes/israel.html', covenant_nations=covenant_nations)
        
    except Exception as e:
        logger.error(f"Error loading Israel tribes: {e}")
        return render_template('error.html', 
                             error_message="Unable to load Israel tribal information"), 500

@tribes_bp.route('/edom')
def edom_dukes():
    """Detailed page for the 12 Dukes of Edom."""
    try:
        edom_dukes = db.query("""
            SELECT nation_code, nation_name, tribe_name, flag_symbol, description,
                   ancestral_description, historical_connection
            FROM biblical_nations 
            WHERE ancestral_group = 'Edom' 
            ORDER BY nation_name
        """)
        
        return render_template('tribes/edom.html', edom_dukes=edom_dukes)
        
    except Exception as e:
        logger.error(f"Error loading Edom dukes: {e}")
        return render_template('error.html', 
                             error_message="Unable to load Edom tribal information"), 500

@tribes_bp.route('/ishmael')
def ishmael_princes():
    """Detailed page for the 12 Princes of Ishmael."""
    try:
        ishmael_princes = db.query("""
            SELECT nation_code, nation_name, tribe_name, flag_symbol, description,
                   ancestral_description, historical_connection
            FROM biblical_nations 
            WHERE ancestral_group = 'Ishmael' 
            ORDER BY nation_name
        """)
        
        return render_template('tribes/ishmael.html', ishmael_princes=ishmael_princes)
        
    except Exception as e:
        logger.error(f"Error loading Ishmael princes: {e}")
        return render_template('error.html', 
                             error_message="Unable to load Ishmael tribal information"), 500

@tribes_bp.route('/hamitic')
def hamitic_nations():
    """Detailed page for Hamitic Nations."""
    try:
        hamitic_nations = db.query("""
            SELECT nation_code, nation_name, tribe_name, flag_symbol, description,
                   ancestral_description, historical_connection
            FROM biblical_nations 
            WHERE ancestral_group = 'Hamitic Nations' 
            ORDER BY nation_name
        """)
        
        return render_template('tribes/hamitic.html', hamitic_nations=hamitic_nations)
        
    except Exception as e:
        logger.error(f"Error loading Hamitic nations: {e}")
        return render_template('error.html', 
                             error_message="Unable to load Hamitic tribal information"), 500

@tribes_bp.route('/api/nations')
def api_nations():
    """API endpoint to get all biblical nations grouped by type."""
    try:
        # Get all nations grouped by ancestral group
        nations = db.query("""
            SELECT nation_code, nation_name, tribe_name, flag_symbol, description,
                   nation_type, ancestral_group, ancestral_description
            FROM biblical_nations 
            ORDER BY nation_type, ancestral_group, nation_name
        """)
        
        # Group nations by type and ancestral group
        grouped_nations = {
            'covenant': [],
            'witness': {
                'Edom': [],
                'Ishmael': [],
                'Hamitic Nations': [],
                'Japhethic Nations': [],
                'Other': []
            }
        }
        
        for nation in nations:
            if nation['nation_type'] == 'covenant':
                grouped_nations['covenant'].append(nation)
            else:
                ancestral_group = nation['ancestral_group'] or 'Other'
                if ancestral_group not in grouped_nations['witness']:
                    grouped_nations['witness'][ancestral_group] = []
                grouped_nations['witness'][ancestral_group].append(nation)
        
        return jsonify({
            'success': True,
            'nations': grouped_nations,
            'summary': {
                'covenant_count': len(grouped_nations['covenant']),
                'edom_count': len(grouped_nations['witness']['Edom']),
                'ishmael_count': len(grouped_nations['witness']['Ishmael']),
                'hamitic_count': len(grouped_nations['witness']['Hamitic Nations']),
                'japhethic_count': len(grouped_nations['witness']['Japhethic Nations']),
                'other_count': len(grouped_nations['witness']['Other'])
            }
        })
        
    except Exception as e:
        logger.error(f"Error in tribes API: {e}")
        return jsonify({'error': 'Failed to load tribal data'}), 500
