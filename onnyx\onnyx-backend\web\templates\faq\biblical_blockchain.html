{% extends "base.html" %}

{% block title %}Biblical Blockchain FAQ - ONNYX Platform{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black">
    <!-- Hero Section -->
    <div class="py-20 relative overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-16">
                <h1 class="text-5xl md:text-7xl font-orbitron font-bold mb-6">
                    <span class="bg-gradient-to-r from-cyber-cyan via-cyber-purple to-cyber-blue bg-clip-text text-transparent">
                        Biblical Blockchain FAQ
                    </span>
                </h1>
                <p class="text-xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
                    Everything you need to know about covenant identity, Gate Keeper verification, 
                    and biblical governance on the ONNYX blockchain.
                </p>
            </div>
        </div>
    </div>

    <!-- FAQ Categories -->
    <div class="py-16 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
                <a href="{{ url_for('faq.biblical_blockchain') }}" 
                   class="glass-card p-6 text-center hover:scale-105 transition-all duration-300 group border-2 border-cyber-cyan">
                    <div class="text-4xl mb-3">📜</div>
                    <h3 class="font-orbitron font-bold text-cyber-cyan mb-2">Biblical Blockchain</h3>
                    <p class="text-sm text-text-secondary">Core concepts and principles</p>
                </a>
                <a href="{{ url_for('faq.gate_keepers') }}" 
                   class="glass-card p-6 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="text-4xl mb-3">⚖️</div>
                    <h3 class="font-orbitron font-bold text-cyber-purple mb-2">Gate Keepers</h3>
                    <p class="text-sm text-text-secondary">Verification and voting system</p>
                </a>
                <a href="{{ url_for('faq.tribal_lineage') }}" 
                   class="glass-card p-6 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="text-4xl mb-3">🏛️</div>
                    <h3 class="font-orbitron font-bold text-cyber-blue mb-2">Tribal Lineage</h3>
                    <p class="text-sm text-text-secondary">Identity and heritage research</p>
                </a>
                <a href="{{ url_for('faq.covenant_economics') }}" 
                   class="glass-card p-6 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="text-4xl mb-3">💎</div>
                    <h3 class="font-orbitron font-bold text-cyber-green mb-2">Covenant Economics</h3>
                    <p class="text-sm text-text-secondary">Biblical tokenomics principles</p>
                </a>
            </div>
        </div>
    </div>

    <!-- FAQ Content -->
    <div class="py-16 relative">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="space-y-8">
                {% for faq in faqs %}
                <div class="glass-card p-8 hover:scale-105 transition-all duration-300">
                    <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-4">
                        {{ faq.question }}
                    </h3>
                    <p class="text-text-secondary leading-relaxed">
                        {{ faq.answer }}
                    </p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Additional Resources -->
    <div class="py-20 relative">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="glass-card-premium p-12">
                <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">
                    Need More Help?
                </h2>
                <p class="text-lg text-text-secondary mb-8 leading-relaxed">
                    Still have questions about biblical blockchain, covenant identity, or the Gate Keeper system? 
                    Explore our other resources or begin your covenant journey.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ url_for('eden_mode.step1') }}"
                       class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-bold text-lg transition-all duration-300 hover:scale-105">
                        🌟 Start Eden Mode
                    </a>
                    <a href="{{ url_for('tribes.overview') }}"
                       class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold text-lg transition-all duration-300 hover:scale-105">
                        🏛️ Explore Tribes
                    </a>
                    <a href="{{ url_for('governance.public_governance') }}"
                       class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold text-lg transition-all duration-300 hover:scale-105">
                        ⚖️ View Governance
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
