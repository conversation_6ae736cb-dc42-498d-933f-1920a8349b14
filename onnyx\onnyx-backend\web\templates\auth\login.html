{% extends "base.html" %}

{% block title %}Access Portal - ONNYX Platform{% endblock %}

{% block content %}
<!-- Fixed fullscreen background -->
<div class="login-background"></div>

<!-- Main content overlay with proper positioning -->
<div class="login-container">
    <!-- Enhanced floating particles with more dynamic movement -->
    <div class="absolute inset-0 w-full h-full overflow-hidden pointer-events-none">
        <!-- Animated particles -->
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full animate-ping"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full animate-pulse"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce"></div>
        <div class="absolute top-2/3 right-1/4 w-1 h-1 bg-cyber-cyan rounded-full animate-ping"></div>
        <div class="absolute top-1/2 left-1/2 w-1 h-1 bg-cyber-purple rounded-full animate-pulse"></div>
        <div class="absolute bottom-1/3 right-1/2 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce"></div>
        <div class="absolute top-1/6 right-1/6 w-1 h-1 bg-cyber-green rounded-full animate-ping"></div>
        <div class="absolute bottom-1/6 left-1/6 w-1.5 h-1.5 bg-cyber-cyan rounded-full animate-pulse"></div>
        
        <!-- Animated grid lines -->
        <div class="absolute inset-0 opacity-10 w-full h-full">
            <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyber-cyan to-transparent animate-pulse"></div>
            <div class="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyber-purple to-transparent animate-pulse" style="animation-delay: 0.5s;"></div>
            <div class="absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyber-blue to-transparent animate-pulse" style="animation-delay: 1s;"></div>
            <div class="absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyber-green to-transparent animate-pulse" style="animation-delay: 1.5s;"></div>
            <!-- Vertical lines -->
            <div class="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-cyber-cyan to-transparent animate-pulse" style="animation-delay: 2s;"></div>
            <div class="absolute top-0 left-1/2 w-px h-full bg-gradient-to-b from-transparent via-cyber-purple to-transparent animate-pulse" style="animation-delay: 2.5s;"></div>
            <div class="absolute top-0 left-3/4 w-px h-full bg-gradient-to-b from-transparent via-cyber-blue to-transparent animate-pulse" style="animation-delay: 3s;"></div>
        </div>
        
        <!-- Floating blockchain data streams -->
        <div class="absolute inset-0 w-full h-full overflow-hidden">
            <div class="blockchain-stream stream-1">01001010110</div>
            <div class="blockchain-stream stream-2">11010001001</div>
            <div class="blockchain-stream stream-3">00110110101</div>
            <div class="blockchain-stream stream-4">10101010011</div>
        </div>
    </div>
    <div class="container-sm relative z-20 w-full max-w-lg mx-auto px-4">
        <!-- Enhanced glass card with better visual hierarchy -->
        <div class="glass-card-premium p-8 rounded-3xl relative overflow-hidden" style="background: rgba(15, 23, 42, 0.95); border: 1px solid rgba(0, 212, 255, 0.3); box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 40px rgba(0, 212, 255, 0.1); backdrop-filter: blur(20px);">
            <!-- Animated border effect -->
            <div class="absolute inset-0 rounded-3xl" style="background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.1), transparent); animation: borderGlow 3s ease-in-out infinite;"></div>
            
            <!-- Enhanced Header with better spacing -->
            <div class="text-center mb-8 relative z-10">
                <!-- Enhanced logo section with quantum effect -->
                <div class="flex items-center justify-center mb-6">
                    <div class="relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-cyber-cyan to-cyber-purple rounded-full blur-lg opacity-30 animate-pulse"></div>
                        <div class="relative bg-gradient-to-br from-onyx-dark to-onyx-black p-4 rounded-2xl border border-cyber-cyan/20">
                            <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                                 alt="ONNYX Logo"
                                 class="w-16 h-16 object-contain quantum-logo"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                            <span class="text-5xl font-black text-cyber-cyan quantum-fallback" style="display: none;">⬢</span>
                        </div>
                        <!-- Quantum particles around logo -->
                        <div class="absolute -top-2 -left-2 w-2 h-2 bg-cyber-cyan rounded-full animate-ping opacity-70"></div>
                        <div class="absolute -bottom-2 -right-2 w-1.5 h-1.5 bg-cyber-purple rounded-full animate-pulse opacity-70"></div>
                        <div class="absolute -top-2 -right-2 w-1 h-1 bg-cyber-blue rounded-full animate-bounce opacity-70"></div>
                        <div class="absolute -bottom-2 -left-2 w-1.5 h-1.5 bg-cyber-green rounded-full animate-ping opacity-70"></div>
                    </div>
                </div>
                
                <!-- Enhanced title with holographic effect -->
                <h1 class="text-4xl font-orbitron font-bold mb-4">
                    <span class="holographic-text bg-gradient-to-r from-cyber-cyan via-cyber-purple to-cyber-blue bg-clip-text text-transparent">
                        Access Portal
                    </span>
                </h1>
                <p class="text-lg text-text-secondary mb-6 cyber-glow">
                    Enter your verified identity credentials
                </p>
                
                <!-- Enhanced status indicator with blockchain animation -->
                <div class="flex items-center justify-center space-x-2 mb-6">
                    <div class="blockchain-indicator">
                        <div class="w-2 h-2 bg-cyber-green rounded-full animate-pulse"></div>
                        <div class="w-2 h-2 bg-cyber-cyan rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
                        <div class="w-2 h-2 bg-cyber-purple rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
                    </div>
                    <span class="text-sm text-cyber-green font-medium">Quantum Network Active</span>
                </div>
            </div>

            <!-- Enhanced Login Form -->
            <div class="relative z-10">
                <form method="POST" class="space-y-6">
                    <!-- Enhanced email input with quantum field effect -->
                    <div class="form-group">
                        <label for="email" class="block text-sm font-medium text-cyber-cyan mb-2">
                            Email Address
                        </label>
                        <div class="relative quantum-field">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <svg class="w-5 h-5 text-cyber-cyan" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                </svg>
                            </div>
                            <input type="email"
                                   id="email"
                                   name="email"
                                   required
                                   class="w-full pl-12 pr-4 py-4 bg-glass-bg border border-glass-border rounded-xl text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-cyber-cyan focus:border-cyber-cyan transition-all duration-300 quantum-input"
                                   placeholder="Enter your registered email"
                                   style="backdrop-filter: blur(10px);">
                            <!-- Quantum field effect -->
                            <div class="quantum-particles"></div>
                        </div>
                    </div>

                    <!-- Enhanced password input with quantum field effect -->
                    <div class="form-group">
                        <label for="password" class="block text-sm font-medium text-cyber-purple mb-2">
                            Password (if set)
                        </label>
                        <div class="relative quantum-field">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <svg class="w-5 h-5 text-cyber-purple" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                            </div>
                            <input type="password"
                                   id="password"
                                   name="password"
                                   class="w-full pl-12 pr-4 py-4 bg-glass-bg border border-glass-border rounded-xl text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-cyber-purple focus:border-cyber-purple transition-all duration-300 quantum-input"
                                   placeholder="Enter your password (optional for some accounts)"
                                   style="backdrop-filter: blur(10px);">
                            <!-- Quantum field effect -->
                            <div class="quantum-particles"></div>
                        </div>
                        <div class="text-xs text-gray-400 mt-1">
                            Leave blank if your account doesn't have a password set
                        </div>
                    </div>

                    <!-- Enhanced security notice with blockchain visualization -->
                    <div class="p-4 rounded-xl border border-cyber-cyan/30 bg-cyber-cyan/5 backdrop-blur-sm security-notice">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0 mt-0.5">
                                <div class="w-8 h-8 bg-gradient-to-r from-cyber-cyan to-cyber-blue rounded-lg flex items-center justify-center security-badge">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.012-3.016A9.956 9.956 0 0121 12c0 1.657-.45 3.212-1.237 4.54M16.5 17a9.956 9.956 0 01-4.5 1c-1.657 0-3.212-.45-4.54-1.237M3 12a9.956 9.956 0 011-4.54m5.5-2.5a9.956 9.956 0 014.5-1c1.657 0 3.212.45 4.54 1.237"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-cyber-cyan font-semibold">Quantum-Resistant Authentication</p>
                                <p class="text-xs text-text-secondary mt-1">
                                    Protected by post-quantum cryptography and biblical covenant verification
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced submit button with blockchain mining effect -->
                    <button type="submit" class="w-full relative group quantum-button">
                        <div class="absolute inset-0 bg-gradient-to-r from-cyber-cyan to-cyber-blue rounded-xl blur-md opacity-40 group-hover:opacity-60 transition-all duration-300"></div>
                        <div class="relative bg-gradient-to-r from-cyber-cyan to-cyber-blue py-4 px-6 rounded-xl font-orbitron font-bold text-lg text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                            <span class="flex items-center justify-center space-x-3">
                                <svg class="w-5 h-5 quantum-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                                </svg>
                                <span>Access Network</span>
                            </span>
                        </div>
                    </button>
                </form>
            </div>

            <!-- Enhanced Footer with centered buttons -->
            <div class="mt-8 relative z-10">
                <div class="text-center">
                    <!-- Enhanced divider with quantum effect -->
                    <div class="relative mb-6">
                        <div class="absolute inset-0 flex items-center">
                            <div class="w-full border-t border-gradient-to-r from-transparent via-cyber-cyan/20 to-transparent quantum-divider"></div>
                        </div>
                        <div class="relative flex justify-center text-sm">
                            <span class="px-6 bg-onyx-black text-text-secondary">New to ONNYX?</span>
                        </div>
                    </div>

                    <!-- Centered action buttons with quantum effects -->
                    <div class="flex flex-col items-center gap-4 mb-6">
                        <a href="{{ url_for('register_choice') }}" class="w-full max-w-sm group relative quantum-button-secondary">
                            <div class="absolute inset-0 bg-gradient-to-r from-cyber-purple to-cyber-blue rounded-xl blur-md opacity-20 group-hover:opacity-40 transition-all duration-300"></div>
                            <div class="relative border-2 border-cyber-purple py-3 px-6 rounded-xl font-orbitron font-semibold text-cyber-purple hover:bg-cyber-purple/10 transition-all duration-300 text-center">
                                <span class="flex items-center justify-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                                    </svg>
                                    <span>Create Identity</span>
                                </span>
                            </div>
                        </a>
                        
                        <a href="{{ url_for('eden_mode.step1') }}" class="w-full max-w-sm group relative quantum-button-secondary">
                            <div class="absolute inset-0 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-xl blur-md opacity-20 group-hover:opacity-40 transition-all duration-300"></div>
                            <div class="relative border-2 border-cyan-400 py-3 px-6 rounded-xl font-orbitron font-semibold text-cyan-400 hover:bg-cyan-400/10 transition-all duration-300 text-center">
                                <span class="flex items-center justify-center space-x-2">
                                    <span>🗿</span>
                                    <span>Eden Mode - Reclaim Your Legacy</span>
                                </span>
                            </div>
                        </a>
                        
                        <a href="{{ url_for('index') }}" class="text-sm text-text-secondary hover:text-cyber-cyan transition-colors duration-300 flex items-center space-x-2 quantum-link">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                            <span>Back to Home</span>
                        </a>
                    </div>

                    <!-- Enhanced platform features with blockchain animations -->
                    <div class="pt-6 border-t border-glass-border">
                        <h3 class="text-sm font-orbitron font-semibold text-cyber-cyan mb-4 flex items-center justify-center space-x-2">
                            <span>⚡</span>
                            <span>Platform Features</span>
                            <span>⚡</span>
                        </h3>
                        <div class="grid grid-cols-1 gap-3">
                            <div class="flex items-center justify-center space-x-3 p-3 rounded-lg bg-cyber-cyan/5 hover:bg-cyber-cyan/10 transition-colors duration-300 feature-card">
                                <div class="w-3 h-3 bg-cyber-cyan rounded-full animate-pulse"></div>
                                <span class="text-sm text-text-secondary">Quantum-resistant security</span>
                                <div class="blockchain-hash">0x4a7b</div>
                            </div>
                            <div class="flex items-center justify-center space-x-3 p-3 rounded-lg bg-cyber-purple/5 hover:bg-cyber-purple/10 transition-colors duration-300 feature-card">
                                <div class="w-3 h-3 bg-cyber-purple rounded-full animate-pulse"></div>
                                <span class="text-sm text-text-secondary">Decentralized validation</span>
                                <div class="blockchain-hash">0x9c2f</div>
                            </div>
                            <div class="flex items-center justify-center space-x-3 p-3 rounded-lg bg-cyber-blue/5 hover:bg-cyber-blue/10 transition-colors duration-300 feature-card">
                                <div class="w-3 h-3 bg-cyber-blue rounded-full animate-pulse"></div>
                                <span class="text-sm text-text-secondary">Biblical covenant network</span>
                                <div class="blockchain-hash">0x1d8e</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Login Page Layout Fixes */
.login-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: -1;
    background: linear-gradient(135deg, 
        rgba(15, 23, 42, 0.95) 0%, 
        rgba(30, 41, 59, 0.9) 25%, 
        rgba(15, 23, 42, 0.95) 50%, 
        rgba(30, 41, 59, 0.9) 75%, 
        rgba(15, 23, 42, 0.95) 100%);
    background-size: 400% 400%;
    animation: gradientShift 8s ease-in-out infinite;
}

.login-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
    animation: pulseOverlay 6s ease-in-out infinite;
}

.login-background::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml,%3csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='none' fill-rule='evenodd'%3e%3cg fill='%2300d4ff' fill-opacity='0.05'%3e%3ccircle cx='30' cy='30' r='1'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e") repeat;
    animation: gridMove 20s linear infinite;
}

.login-container {
    position: relative;
    z-index: 10;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6rem 1rem 2rem; /* Increased top padding to clear navigation */
    margin-top: 0; /* Remove negative margin that was causing overlap */
}

/* Enhanced animations */
@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes pulseOverlay {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(60px, 60px); }
}

/* Blockchain streams */
.blockchain-stream {
    position: absolute;
    font-family: 'Courier New', monospace;
    font-size: 10px;
    color: rgba(0, 212, 255, 0.3);
    white-space: nowrap;
    pointer-events: none;
    animation: streamFlow 8s linear infinite;
}

.stream-1 {
    top: 20%;
    left: -10%;
    animation-delay: 0s;
}

.stream-2 {
    top: 40%;
    right: -10%;
    animation-delay: 2s;
    animation-direction: reverse;
}

.stream-3 {
    top: 60%;
    left: -10%;
    animation-delay: 4s;
}

.stream-4 {
    top: 80%;
    right: -10%;
    animation-delay: 6s;
    animation-direction: reverse;
}

@keyframes streamFlow {
    0% { transform: translateX(0); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateX(120vw); opacity: 0; }
}

/* Enhanced CSS for quantum effects and blockchain animations */
.holographic-text {
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5), 0 0 20px rgba(154, 0, 255, 0.3);
    animation: holographicFlicker 2s ease-in-out infinite;
}

@keyframes holographicFlicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.9; }
}

.cyber-glow {
    text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
}

.blockchain-indicator {
    display: flex;
    gap: 4px;
    animation: blockchainPulse 2s ease-in-out infinite;
}

@keyframes blockchainPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.quantum-field {
    position: relative;
    overflow: hidden;
}

.quantum-input:focus + .quantum-particles::before {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at center, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
    animation: quantumField 1s ease-in-out;
}

@keyframes quantumField {
    0% { opacity: 0; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1.1); }
    100% { opacity: 0; transform: scale(1); }
}

.security-notice {
    position: relative;
    overflow: hidden;
}

.security-notice::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    animation: securityScan 3s ease-in-out infinite;
}

@keyframes securityScan {
    0% { left: -100%; }
    100% { left: 100%; }
}

.security-badge {
    animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
    0%, 100% { box-shadow: 0 0 10px rgba(0, 212, 255, 0.3); }
    50% { box-shadow: 0 0 20px rgba(0, 212, 255, 0.6); }
}

.quantum-button:hover .quantum-icon {
    animation: quantumSpin 0.5s ease-in-out;
}

@keyframes quantumSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes borderGlow {
    0%, 100% { 
        background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.1), transparent);
        opacity: 1;
    }
    50% { 
        background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.3), transparent);
        opacity: 0.8;
    }
}

/* Feature card animations */
.feature-card {
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 212, 255, 0.1);
}

.blockchain-hash {
    font-family: 'Courier New', monospace;
    font-size: 10px;
    color: rgba(0, 212, 255, 0.5);
    opacity: 0.7;
}

/* Quantum logo effects */
.quantum-logo {
    transition: all 0.3s ease;
}

.quantum-logo:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.5));
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .login-container {
        padding: 5rem 1rem 1rem; /* Adjusted for mobile navigation height */
    }
    
    .blockchain-stream {
        font-size: 8px;
    }
    
    .glass-card-premium {
        margin-top: 1rem; /* Additional margin for mobile */
    }
}

.quantum-link:hover {
    text-shadow: 0 0 5px rgba(0, 212, 255, 0.5);
}

.blockchain-stream {
    position: absolute;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: rgba(0, 212, 255, 0.3);
    animation: streamFlow 10s linear infinite;
    pointer-events: none;
}

.stream-1 {
    top: 20%;
    left: -100px;
    animation-delay: 0s;
}

.stream-2 {
    top: 40%;
    left: -100px;
    animation-delay: 2s;
    color: rgba(154, 0, 255, 0.3);
}

.stream-3 {
    top: 60%;
    left: -100px;
    animation-delay: 4s;
    color: rgba(0, 128, 255, 0.3);
}

.stream-4 {
    top: 80%;
    left: -100px;
    animation-delay: 6s;
    color: rgba(0, 255, 136, 0.3);
}

@keyframes streamFlow {
    0% { transform: translateX(-100px); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateX(calc(100vw + 100px)); opacity: 0; }
}

.blockchain-hash {
    font-family: 'Courier New', monospace;
    font-size: 10px;
    color: rgba(0, 212, 255, 0.6);
    background: rgba(0, 212, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid rgba(0, 212, 255, 0.2);
}

.feature-card:hover .blockchain-hash {
    animation: hashFlicker 0.5s ease-in-out;
}

@keyframes hashFlicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.quantum-logo {
    animation: logoQuantum 3s ease-in-out infinite;
}

@keyframes logoQuantum {
    0%, 100% { filter: brightness(1) saturate(1); }
    50% { filter: brightness(1.2) saturate(1.3); }
}

.quantum-divider {
    animation: dividerGlow 2s ease-in-out infinite;
}

@keyframes dividerGlow {
    0%, 100% { opacity: 0.2; }
    50% { opacity: 0.4; }
}

@keyframes borderGlow {
    0%, 100% { opacity: 0.1; }
    50% { opacity: 0.3; }
}

/* Ensure full viewport coverage */
.hero-gradient {
    background: linear-gradient(135deg, 
        rgba(15, 23, 42, 0.95) 0%,
        rgba(30, 41, 59, 0.9) 25%,
        rgba(15, 23, 42, 0.95) 50%,
        rgba(30, 41, 59, 0.9) 75%,
        rgba(15, 23, 42, 0.95) 100%
    );
}

.cyber-grid {
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(154, 0, 255, 0.05) 0%, transparent 50%),
        linear-gradient(0deg, transparent 24%, rgba(0, 212, 255, 0.02) 25%, rgba(0, 212, 255, 0.02) 26%, transparent 27%, transparent 74%, rgba(0, 212, 255, 0.02) 75%, rgba(0, 212, 255, 0.02) 76%, transparent 77%, transparent),
        linear-gradient(90deg, transparent 24%, rgba(0, 212, 255, 0.02) 25%, rgba(0, 212, 255, 0.02) 26%, transparent 27%, transparent 74%, rgba(0, 212, 255, 0.02) 75%, rgba(0, 212, 255, 0.02) 76%, transparent 77%, transparent);
    background-size: 100px 100px;
    background-position: 0 0, 0 0, 0 0, 0 0;
}
</style>

<script>
// Enhanced form validation and quantum animations
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const emailInput = document.getElementById('email');
    const submitButton = form.querySelector('button[type="submit"]');

    // Email validation with quantum visual feedback
    emailInput.addEventListener('input', function() {
        const email = this.value.trim();
        const isValid = email && email.includes('@') && email.includes('.');

        if (isValid) {
            this.classList.remove('border-red-500/50');
            this.classList.add('border-cyber-cyan/50');
            this.style.boxShadow = '0 0 20px rgba(0, 212, 255, 0.3)';
        } else if (email) {
            this.classList.remove('border-cyber-cyan/50');
            this.classList.add('border-red-500/50');
            this.style.boxShadow = '0 0 20px rgba(239, 68, 68, 0.3)';
        } else {
            this.classList.remove('border-red-500/50', 'border-cyber-cyan/50');
            this.style.boxShadow = 'none';
        }
    });

    // Enhanced form submission with blockchain mining animation
    form.addEventListener('submit', function(e) {
        const email = emailInput.value.trim();

        if (!email || !email.includes('@')) {
            e.preventDefault();
            emailInput.focus();
            emailInput.style.boxShadow = '0 0 20px rgba(239, 68, 68, 0.5)';
            return;
        }

        // Show blockchain mining state
        submitButton.disabled = true;
        submitButton.innerHTML = `
            <span class="flex items-center justify-center space-x-3">
                <div class="blockchain-mining">
                    <div class="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin"></div>
                    <div class="w-2 h-2 bg-cyber-cyan rounded-full animate-ping ml-2"></div>
                    <div class="w-2 h-2 bg-cyber-purple rounded-full animate-pulse ml-1"></div>
                </div>
                <span>Mining Block...</span>
            </span>
        `;
    });

    // Auto-focus email field with quantum delay
    setTimeout(() => {
        emailInput.focus();
        emailInput.style.transition = 'all 0.5s ease-in-out';
        emailInput.style.transform = 'scale(1.02)';
        setTimeout(() => {
            emailInput.style.transform = 'scale(1)';
        }, 200);
    }, 500);

    // Add quantum particles to logo
    const logo = document.querySelector('.quantum-logo');
    if (logo) {
        setInterval(() => {
            const particle = document.createElement('div');
            particle.className = 'quantum-particle';
            particle.style.cssText = `
                position: absolute;
                width: 2px;
                height: 2px;
                background: rgba(0, 212, 255, 0.6);
                border-radius: 50%;
                pointer-events: none;
                animation: particleFloat 2s ease-out forwards;
            `;
            logo.parentElement.appendChild(particle);
            
            setTimeout(() => {
                particle.remove();
            }, 2000);
        }, 1000);
    }
});

// Add particle float animation
const style = document.createElement('style');
style.textContent = `
    @keyframes particleFloat {
        0% {
            transform: translate(0, 0) scale(1);
            opacity: 1;
        }
        100% {
            transform: translate(${Math.random() * 200 - 100}px, ${Math.random() * 200 - 100}px) scale(0);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
