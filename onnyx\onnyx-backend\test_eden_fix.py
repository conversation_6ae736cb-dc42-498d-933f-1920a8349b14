#!/usr/bin/env python3
"""
Test Eden Mode Fix

Simple test to verify that Eden Mode identity creation now works.
"""

import sys
import os
import time
import json

# Add the current directory to the Python path
sys.path.insert(0, os.path.abspath('.'))

from shared.db.db import db
from web.routes.eden_mode import get_nation_id_for_tribe

def test_nation_mapping():
    """Test the nation mapping function"""
    print("🧪 Testing nation mapping function...")
    
    test_tribes = ["Judah", "<PERSON>", "Levi", "Issachar"]
    
    for tribe in test_tribes:
        nation_id = get_nation_id_for_tribe(tribe)
        print(f"   {tribe} -> {nation_id}")
    
    print("✅ Nation mapping test complete")

def test_eden_identity_creation():
    """Test Eden Mode identity creation"""
    print("🧪 Testing Eden Mode identity creation...")
    
    # Test data
    test_identity = {
        'identity_id': 'TEST_EDEN_FIX_123',
        'name': 'Test Eden Fix User',
        'email': '<EMAIL>',
        'public_key': 'test_public_key_fix',
        'nation_id': get_nation_id_for_tribe('Judah'),
        'tribal_affiliation': 'judah',
        'status': 'active',
        'role_class': 'citizen',
        'verification_level': 1,
        'covenant_accepted': True,
        'eden_mode_completed': True,
        'metadata': json.dumps({'test': True, 'eden_fix': True}),
        'created_at': int(time.time()),
        'updated_at': int(time.time())
    }
    
    try:
        # Insert identity
        columns = ', '.join(test_identity.keys())
        placeholders = ', '.join(['?' for _ in test_identity.keys()])
        values = list(test_identity.values())
        
        db.execute(f"INSERT INTO identities ({columns}) VALUES ({placeholders})", values)
        
        # Verify
        result = db.query_one("SELECT identity_id, name, nation_id FROM identities WHERE identity_id = ?", 
                             (test_identity['identity_id'],))
        
        if result:
            print("✅ Eden Mode identity creation SUCCESSFUL!")
            print(f"   Identity: {result[0]}")
            print(f"   Name: {result[1]}")
            print(f"   Nation ID: {result[2]}")

            # Clean up
            db.execute("DELETE FROM identities WHERE identity_id = ?", (test_identity['identity_id'],))
            print("✅ Test data cleaned up")
            return True
        else:
            print("❌ Eden Mode identity creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function"""
    print("🔧 Testing Eden Mode Foreign Key Fix")
    print("=" * 50)
    
    try:
        # Test 1: Nation mapping
        test_nation_mapping()
        print()
        
        # Test 2: Identity creation
        success = test_eden_identity_creation()
        print()
        
        if success:
            print("🎉 ALL TESTS PASSED!")
            print("✅ Eden Mode should now work without foreign key errors")
        else:
            print("❌ Tests failed")
            return 1
        
        return 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
