-- Onnyx Production Database Schema
-- Minimal schema for production readiness

-- Core identities table (no foreign key dependencies)
CREATE TABLE IF NOT EXISTS identities (
    identity_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT,
    public_key TEXT NOT NULL,
    nation_id TEXT,
    metadata TEXT NOT NULL DEFAULT '{}',
    status TEXT NOT NULL DEFAULT 'active',
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    -- Tokenomics fields for biblical economic principles
    last_active_timestamp INTEGER DEFAULT 0,
    last_transaction_height INTEGER DEFAULT 0,
    deeds_score REAL DEFAULT 0.0,
    sabbath_observer BOOLEAN DEFAULT 0,
    -- CIPP (Covenant Identity Protection Protocol) fields
    nation_of_origin TEXT DEFAULT 'Judah',
    role_class TEXT DEFAULT 'Citizen',
    etzem_score INTEGER DEFAULT 0,
    zeman_count INTEGER DEFAULT 0,
    protection_tier TEXT DEFAULT 'Basic',
    verification_level INTEGER DEFAULT 0,
    covenant_accepted BOOLEAN DEFAULT 0,
    vault_status TEXT DEFAULT 'Active',
    last_activity_season INTEGER DEFAULT 0,
    nation_code TEXT DEFAULT 'IL',
    nation_name TEXT DEFAULT 'Israel',
    timezone TEXT DEFAULT 'UTC'
);

CREATE INDEX IF NOT EXISTS idx_identities_name ON identities(name);
CREATE INDEX IF NOT EXISTS idx_identities_email ON identities(email);

-- Core transactions table
CREATE TABLE IF NOT EXISTS transactions (
    tx_id TEXT PRIMARY KEY,
    block_hash TEXT,
    timestamp INTEGER NOT NULL,
    op TEXT NOT NULL,
    data TEXT NOT NULL DEFAULT '{}',
    sender TEXT NOT NULL,
    signature TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    created_at INTEGER NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_transactions_sender ON transactions(sender);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_timestamp ON transactions(timestamp);

-- Core blocks table
CREATE TABLE IF NOT EXISTS blocks (
    block_hash TEXT PRIMARY KEY,
    block_height INTEGER NOT NULL UNIQUE,
    previous_hash TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    miner TEXT NOT NULL,
    transactions TEXT NOT NULL DEFAULT '[]',
    merkle_root TEXT NOT NULL,
    nonce INTEGER NOT NULL DEFAULT 0,
    difficulty INTEGER NOT NULL DEFAULT 1,
    created_at INTEGER NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_blocks_height ON blocks(block_height);
CREATE INDEX IF NOT EXISTS idx_blocks_timestamp ON blocks(timestamp);

-- Mempool table
CREATE TABLE IF NOT EXISTS mempool (
    tx_id TEXT PRIMARY KEY,
    timestamp INTEGER NOT NULL,
    op TEXT NOT NULL,
    data TEXT NOT NULL DEFAULT '{}',
    sender TEXT NOT NULL,
    signature TEXT NOT NULL,
    created_at INTEGER NOT NULL
);

-- Tokens table
CREATE TABLE IF NOT EXISTS tokens (
    token_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    symbol TEXT NOT NULL,
    creator_id TEXT NOT NULL,
    supply INTEGER NOT NULL DEFAULT 0,
    category TEXT NOT NULL,
    decimals INTEGER NOT NULL DEFAULT 0,
    created_at INTEGER NOT NULL,
    metadata TEXT NOT NULL DEFAULT '{}'
);

CREATE INDEX IF NOT EXISTS idx_tokens_creator ON tokens(creator_id);
CREATE INDEX IF NOT EXISTS idx_tokens_symbol ON tokens(symbol);

-- Token balances table
CREATE TABLE IF NOT EXISTS token_balances (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    token_id TEXT NOT NULL,
    identity_id TEXT NOT NULL,
    balance INTEGER NOT NULL DEFAULT 0,
    updated_at INTEGER NOT NULL,
    UNIQUE(token_id, identity_id)
);

CREATE INDEX IF NOT EXISTS idx_token_balances_token ON token_balances(token_id);
CREATE INDEX IF NOT EXISTS idx_token_balances_identity ON token_balances(identity_id);

-- Sela business registry table (Enhanced for Phase 1 Production)
CREATE TABLE IF NOT EXISTS selas (
    sela_id TEXT PRIMARY KEY,
    identity_id TEXT NOT NULL,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    description TEXT,
    address TEXT,
    phone TEXT,
    website TEXT,
    services TEXT,
    stake_amount INTEGER NOT NULL DEFAULT 0,
    stake_token_id TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    trust_score REAL DEFAULT 0.0,
    mining_tier TEXT DEFAULT 'basic',
    mining_power INTEGER DEFAULT 1,
    mining_rewards_earned REAL DEFAULT 0.0,
    blocks_mined INTEGER DEFAULT 0,
    onx_balance REAL DEFAULT 0.0,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    metadata TEXT NOT NULL DEFAULT '{}'
);

CREATE INDEX IF NOT EXISTS idx_selas_identity ON selas(identity_id);
CREATE INDEX IF NOT EXISTS idx_selas_status ON selas(status);

-- Activity ledger table
CREATE TABLE IF NOT EXISTS activity_ledger (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id TEXT NOT NULL,
    sela_id TEXT,
    activity_type TEXT NOT NULL,
    data TEXT NOT NULL DEFAULT '{}',
    timestamp INTEGER NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_activity_ledger_identity ON activity_ledger(identity_id);
CREATE INDEX IF NOT EXISTS idx_activity_ledger_sela ON activity_ledger(sela_id);
CREATE INDEX IF NOT EXISTS idx_activity_ledger_timestamp ON activity_ledger(timestamp);

-- Event logs table
CREATE TABLE IF NOT EXISTS event_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    event_type TEXT NOT NULL,
    entity_id TEXT NOT NULL,
    data TEXT NOT NULL DEFAULT '{}',
    timestamp INTEGER NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_event_logs_type ON event_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_event_logs_entity ON event_logs(entity_id);
CREATE INDEX IF NOT EXISTS idx_event_logs_timestamp ON event_logs(timestamp);

-- Chain parameters table
CREATE TABLE IF NOT EXISTS chain_parameters (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    default_value TEXT NOT NULL,
    description TEXT,
    category TEXT,
    last_updated INTEGER,
    metadata TEXT DEFAULT '{}'
);

CREATE INDEX IF NOT EXISTS idx_chain_parameters_category ON chain_parameters(category);

-- Biblical Tokenomics Tables

-- Jubilee pools for wealth redistribution
CREATE TABLE IF NOT EXISTS jubilee_pools (
    pool_id TEXT PRIMARY KEY,
    pool_type TEXT NOT NULL, -- 'GLEANING', 'COMMUNITY', 'FIRSTFRUITS'
    total_amount REAL NOT NULL DEFAULT 0.0,
    token_id TEXT NOT NULL DEFAULT 'ONX',
    created_at INTEGER NOT NULL,
    last_distribution INTEGER DEFAULT 0,
    metadata TEXT NOT NULL DEFAULT '{}'
);

-- Dormant accounts tracking for jubilee reset
CREATE TABLE IF NOT EXISTS dormant_accounts (
    identity_id TEXT PRIMARY KEY,
    last_activity INTEGER NOT NULL,
    dormant_since INTEGER NOT NULL,
    reclaimed_amount REAL DEFAULT 0.0,
    status TEXT DEFAULT 'DORMANT', -- 'DORMANT', 'RECLAIMED', 'REACTIVATED'
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Deed tracking for righteous activities
CREATE TABLE IF NOT EXISTS deeds_ledger (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id TEXT NOT NULL,
    deed_type TEXT NOT NULL, -- 'MUTUAL_AID', 'DONATION', 'FIRSTFRUITS', 'SABBATH_OBSERVANCE'
    deed_value REAL NOT NULL,
    description TEXT,
    timestamp INTEGER NOT NULL,
    block_height INTEGER,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Lending system for anti-usury loans
CREATE TABLE IF NOT EXISTS loans (
    loan_id TEXT PRIMARY KEY,
    lender_id TEXT NOT NULL,
    borrower_id TEXT NOT NULL,
    amount REAL NOT NULL,
    token_id TEXT NOT NULL DEFAULT 'ONX',
    grace_blocks INTEGER NOT NULL,
    forgiveness_threshold REAL DEFAULT 0.8,
    amount_paid REAL DEFAULT 0.0,
    status TEXT DEFAULT 'ACTIVE', -- 'ACTIVE', 'FORGIVEN', 'REPAID'
    created_at INTEGER NOT NULL,
    created_block INTEGER NOT NULL,
    FOREIGN KEY (lender_id) REFERENCES identities(identity_id),
    FOREIGN KEY (borrower_id) REFERENCES identities(identity_id)
);

-- Token classification system
CREATE TABLE IF NOT EXISTS token_classes (
    token_id TEXT PRIMARY KEY,
    class_type TEXT NOT NULL, -- 'Avodah', 'Zedek', 'Yovel', 'Etzem'
    class_metadata TEXT DEFAULT '{}',
    assigned_at INTEGER NOT NULL,
    FOREIGN KEY (token_id) REFERENCES tokens(token_id)
);

-- Sabbath enforcement tracking
CREATE TABLE IF NOT EXISTS sabbath_periods (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    start_timestamp INTEGER NOT NULL,
    end_timestamp INTEGER NOT NULL,
    block_height_start INTEGER,
    block_height_end INTEGER,
    observers_count INTEGER DEFAULT 0
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_dormant_accounts_status ON dormant_accounts(status);
CREATE INDEX IF NOT EXISTS idx_deeds_ledger_identity ON deeds_ledger(identity_id);
CREATE INDEX IF NOT EXISTS idx_deeds_ledger_type ON deeds_ledger(deed_type);
CREATE INDEX IF NOT EXISTS idx_loans_borrower ON loans(borrower_id);
CREATE INDEX IF NOT EXISTS idx_loans_lender ON loans(lender_id);
CREATE INDEX IF NOT EXISTS idx_loans_status ON loans(status);
CREATE INDEX IF NOT EXISTS idx_sabbath_periods_timestamps ON sabbath_periods(start_timestamp, end_timestamp);

-- CIPP (Covenant Identity Protection Protocol) Tables

-- Biblical nations registry
CREATE TABLE IF NOT EXISTS biblical_nations (
    nation_code TEXT PRIMARY KEY,
    nation_name TEXT NOT NULL,
    tribe_name TEXT NOT NULL,
    description TEXT,
    flag_symbol TEXT,
    covenant_status TEXT DEFAULT 'Active',
    created_at INTEGER NOT NULL
);

-- Insert biblical nations
INSERT OR IGNORE INTO biblical_nations (nation_code, nation_name, tribe_name, description, flag_symbol, created_at) VALUES
('JU', 'Judah', 'Tribe of Judah', 'The royal tribe, keepers of the scepter', '🦁', strftime('%s', 'now')),
('BE', 'Benjamin', 'Tribe of Benjamin', 'The beloved tribe, warriors and protectors', '🐺', strftime('%s', 'now')),
('EP', 'Ephraim', 'Tribe of Ephraim', 'The fruitful tribe, leaders of the northern kingdom', '🌾', strftime('%s', 'now')),
('MA', 'Manasseh', 'Tribe of Manasseh', 'The forgetful tribe, blessed with abundance', '🌳', strftime('%s', 'now')),
('LE', 'Levi', 'Tribe of Levi', 'The priestly tribe, servants of the sanctuary', '⚖️', strftime('%s', 'now')),
('IS', 'Issachar', 'Tribe of Issachar', 'The wise tribe, discerners of times and seasons', '📚', strftime('%s', 'now')),
('ZE', 'Zebulun', 'Tribe of Zebulun', 'The merchant tribe, dwellers by the sea', '⛵', strftime('%s', 'now')),
('RU', 'Reuben', 'Tribe of Reuben', 'The firstborn tribe, unstable as water', '💧', strftime('%s', 'now')),
('GA', 'Gad', 'Tribe of Gad', 'The warrior tribe, overcomers of adversity', '⚔️', strftime('%s', 'now')),
('AS', 'Asher', 'Tribe of Asher', 'The blessed tribe, providers of royal dainties', '🍯', strftime('%s', 'now')),
('NA', 'Naphtali', 'Tribe of Naphtali', 'The swift tribe, messengers of good tidings', '🦌', strftime('%s', 'now')),
('DA', 'Dan', 'Tribe of Dan', 'The judging tribe, serpents by the way', '🐍', strftime('%s', 'now')),
('SI', 'Simeon', 'Tribe of Simeon', 'The hearing tribe, scattered among brothers', '👂', strftime('%s', 'now'));

-- Verification progress tracking
CREATE TABLE IF NOT EXISTS verification_progress (
    identity_id TEXT PRIMARY KEY,
    tier_0_completed BOOLEAN DEFAULT 1,
    tier_1_completed BOOLEAN DEFAULT 0,
    tier_2_completed BOOLEAN DEFAULT 0,
    tier_3_completed BOOLEAN DEFAULT 0,
    tier_1_method TEXT, -- '2FA', 'BIOMETRIC', 'VALIDATOR_ENDORSEMENT'
    tier_2_contributions INTEGER DEFAULT 0,
    tier_3_governance_count INTEGER DEFAULT 0,
    last_updated INTEGER NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Protection requests and incidents
CREATE TABLE IF NOT EXISTS protection_requests (
    request_id TEXT PRIMARY KEY,
    identity_id TEXT NOT NULL,
    request_type TEXT NOT NULL, -- 'EMERGENCY_FREEZE', 'VAULT_LOCK', 'EXILE_MODE'
    reason TEXT,
    status TEXT DEFAULT 'PENDING', -- 'PENDING', 'APPROVED', 'DENIED', 'ACTIVE'
    requested_at INTEGER NOT NULL,
    processed_at INTEGER,
    processed_by TEXT,
    metadata TEXT DEFAULT '{}',
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Covenant scroll acceptance tracking
CREATE TABLE IF NOT EXISTS covenant_acceptances (
    identity_id TEXT PRIMARY KEY,
    scroll_version TEXT NOT NULL DEFAULT 'v1.0',
    accepted_at INTEGER NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    signature_hash TEXT,
    witness_count INTEGER DEFAULT 0,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Etzem score history for reputation tracking
CREATE TABLE IF NOT EXISTS etzem_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id TEXT NOT NULL,
    old_score INTEGER NOT NULL,
    new_score INTEGER NOT NULL,
    change_reason TEXT NOT NULL,
    change_amount INTEGER NOT NULL,
    timestamp INTEGER NOT NULL,
    block_height INTEGER,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Zeman (seasonal activity) tracking
CREATE TABLE IF NOT EXISTS zeman_activities (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id TEXT NOT NULL,
    season_start INTEGER NOT NULL,
    season_end INTEGER NOT NULL,
    activity_count INTEGER DEFAULT 0,
    contribution_value REAL DEFAULT 0.0,
    season_score INTEGER DEFAULT 0,
    completed BOOLEAN DEFAULT 0,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Vault security events
CREATE TABLE IF NOT EXISTS vault_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    identity_id TEXT NOT NULL,
    event_type TEXT NOT NULL, -- 'LOCK', 'UNLOCK', 'FREEZE', 'EXILE_ACTIVATE', 'EXILE_DEACTIVATE'
    triggered_by TEXT, -- 'USER', 'ADMIN', 'SYSTEM', 'EMERGENCY'
    reason TEXT,
    timestamp INTEGER NOT NULL,
    metadata TEXT DEFAULT '{}',
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Create indexes for CIPP tables
CREATE INDEX IF NOT EXISTS idx_verification_progress_identity ON verification_progress(identity_id);
CREATE INDEX IF NOT EXISTS idx_protection_requests_identity ON protection_requests(identity_id);
CREATE INDEX IF NOT EXISTS idx_protection_requests_status ON protection_requests(status);
CREATE INDEX IF NOT EXISTS idx_covenant_acceptances_identity ON covenant_acceptances(identity_id);
CREATE INDEX IF NOT EXISTS idx_etzem_history_identity ON etzem_history(identity_id);
CREATE INDEX IF NOT EXISTS idx_etzem_history_timestamp ON etzem_history(timestamp);
CREATE INDEX IF NOT EXISTS idx_zeman_activities_identity ON zeman_activities(identity_id);
CREATE INDEX IF NOT EXISTS idx_zeman_activities_season ON zeman_activities(season_start, season_end);
CREATE INDEX IF NOT EXISTS idx_vault_events_identity ON vault_events(identity_id);
CREATE INDEX IF NOT EXISTS idx_vault_events_type ON vault_events(event_type);
CREATE INDEX IF NOT EXISTS idx_identities_nation_origin ON identities(nation_of_origin);
CREATE INDEX IF NOT EXISTS idx_identities_role_class ON identities(role_class);
CREATE INDEX IF NOT EXISTS idx_identities_protection_tier ON identities(protection_tier);
CREATE INDEX IF NOT EXISTS idx_identities_verification_level ON identities(verification_level);

-- Covenant Labor System Tables

-- Labor records for tracking all economic activities
CREATE TABLE IF NOT EXISTS labor_records (
    labor_id TEXT PRIMARY KEY,
    identity_id TEXT NOT NULL,
    sela_id TEXT, -- Optional for independent workers
    labor_type TEXT NOT NULL, -- 'service', 'product', 'teaching', 'healing', 'farming', 'governance', 'community'
    description TEXT NOT NULL,
    value_estimate REAL DEFAULT 0.0, -- Estimated economic value
    timestamp INTEGER NOT NULL,
    verified_by TEXT, -- Identity ID of verifier
    verification_status TEXT DEFAULT 'pending', -- 'pending', 'verified', 'disputed', 'rejected'
    mikvah_eligible BOOLEAN DEFAULT 1, -- Qualifies for biblical tokenomics rewards
    etzem_points INTEGER DEFAULT 0, -- Reputation points earned
    season_period INTEGER, -- Zeman season identifier
    metadata TEXT DEFAULT '{}', -- Additional labor details
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    FOREIGN KEY (sela_id) REFERENCES selas(sela_id),
    FOREIGN KEY (verified_by) REFERENCES identities(identity_id)
);

-- Labor verification tracking
CREATE TABLE IF NOT EXISTS labor_verifications (
    verification_id TEXT PRIMARY KEY,
    labor_id TEXT NOT NULL,
    verifier_id TEXT NOT NULL,
    verification_type TEXT NOT NULL, -- 'approve', 'dispute', 'reject'
    verification_notes TEXT,
    etzem_adjustment INTEGER DEFAULT 0,
    timestamp INTEGER NOT NULL,
    FOREIGN KEY (labor_id) REFERENCES labor_records(labor_id),
    FOREIGN KEY (verifier_id) REFERENCES identities(identity_id)
);

-- Mikvah token transactions for biblical tokenomics rewards
CREATE TABLE IF NOT EXISTS mikvah_transactions (
    transaction_id TEXT PRIMARY KEY,
    identity_id TEXT NOT NULL,
    labor_id TEXT, -- Optional link to labor record
    transaction_type TEXT NOT NULL, -- 'labor_reward', 'gleaning_distribution', 'yovel_redistribution', 'sabbath_bonus'
    amount REAL NOT NULL,
    balance_before REAL DEFAULT 0.0,
    balance_after REAL DEFAULT 0.0,
    season_period INTEGER,
    yovel_cycle INTEGER, -- 7-year jubilee cycle
    metadata TEXT DEFAULT '{}',
    timestamp INTEGER NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    FOREIGN KEY (labor_id) REFERENCES labor_records(labor_id)
);

-- Seasonal labor summaries for Zeman tracking
CREATE TABLE IF NOT EXISTS seasonal_labor_summaries (
    summary_id TEXT PRIMARY KEY,
    identity_id TEXT NOT NULL,
    season_period INTEGER NOT NULL,
    season_start INTEGER NOT NULL,
    season_end INTEGER NOT NULL,
    total_labor_count INTEGER DEFAULT 0,
    total_value_estimate REAL DEFAULT 0.0,
    total_etzem_earned INTEGER DEFAULT 0,
    total_mikvah_earned REAL DEFAULT 0.0,
    verification_rate REAL DEFAULT 0.0, -- Percentage of labor verified
    sabbath_compliance BOOLEAN DEFAULT 1,
    season_completed BOOLEAN DEFAULT 0,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id)
);

-- Gleaning pool for community support
CREATE TABLE IF NOT EXISTS gleaning_pool (
    pool_id TEXT PRIMARY KEY,
    season_period INTEGER NOT NULL,
    total_contributions REAL DEFAULT 0.0,
    total_distributions REAL DEFAULT 0.0,
    current_balance REAL DEFAULT 0.0,
    contributor_count INTEGER DEFAULT 0,
    recipient_count INTEGER DEFAULT 0,
    last_updated INTEGER NOT NULL
);

-- Gleaning distributions to community members
CREATE TABLE IF NOT EXISTS gleaning_distributions (
    distribution_id TEXT PRIMARY KEY,
    identity_id TEXT NOT NULL,
    pool_id TEXT NOT NULL,
    amount REAL NOT NULL,
    reason TEXT, -- 'hardship', 'emergency', 'community_support'
    approved_by TEXT, -- Identity ID of approver
    timestamp INTEGER NOT NULL,
    FOREIGN KEY (identity_id) REFERENCES identities(identity_id),
    FOREIGN KEY (pool_id) REFERENCES gleaning_pool(pool_id),
    FOREIGN KEY (approved_by) REFERENCES identities(identity_id)
);

-- Sabbath periods for enforcement
CREATE TABLE IF NOT EXISTS sabbath_enforcement (
    period_id TEXT PRIMARY KEY,
    start_timestamp INTEGER NOT NULL,
    end_timestamp INTEGER NOT NULL,
    period_type TEXT NOT NULL, -- 'weekly', 'annual', 'jubilee'
    active BOOLEAN DEFAULT 1,
    created_at INTEGER NOT NULL
);

-- Create indexes for labor system
CREATE INDEX IF NOT EXISTS idx_labor_records_identity ON labor_records(identity_id);
CREATE INDEX IF NOT EXISTS idx_labor_records_sela ON labor_records(sela_id);
CREATE INDEX IF NOT EXISTS idx_labor_records_type ON labor_records(labor_type);
CREATE INDEX IF NOT EXISTS idx_labor_records_status ON labor_records(verification_status);
CREATE INDEX IF NOT EXISTS idx_labor_records_timestamp ON labor_records(timestamp);
CREATE INDEX IF NOT EXISTS idx_labor_records_season ON labor_records(season_period);
CREATE INDEX IF NOT EXISTS idx_labor_verifications_labor ON labor_verifications(labor_id);
CREATE INDEX IF NOT EXISTS idx_labor_verifications_verifier ON labor_verifications(verifier_id);
CREATE INDEX IF NOT EXISTS idx_mikvah_transactions_identity ON mikvah_transactions(identity_id);
CREATE INDEX IF NOT EXISTS idx_mikvah_transactions_type ON mikvah_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_mikvah_transactions_timestamp ON mikvah_transactions(timestamp);
CREATE INDEX IF NOT EXISTS idx_seasonal_summaries_identity ON seasonal_labor_summaries(identity_id);
CREATE INDEX IF NOT EXISTS idx_seasonal_summaries_period ON seasonal_labor_summaries(season_period);
CREATE INDEX IF NOT EXISTS idx_gleaning_distributions_identity ON gleaning_distributions(identity_id);
CREATE INDEX IF NOT EXISTS idx_gleaning_distributions_timestamp ON gleaning_distributions(timestamp);
CREATE INDEX IF NOT EXISTS idx_sabbath_enforcement_timestamps ON sabbath_enforcement(start_timestamp, end_timestamp);
