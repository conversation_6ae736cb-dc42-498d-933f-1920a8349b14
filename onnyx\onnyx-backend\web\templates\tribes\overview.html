{% extends "base.html" %}

{% block title %}Biblical Tribes Overview - ONNYX Platform{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black">
    <!-- Hero Section -->
    <div class="py-20 relative overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-16">
                <h1 class="text-5xl md:text-7xl font-orbitron font-bold mb-6">
                    <span class="bg-gradient-to-r from-cyber-cyan via-cyber-purple to-cyber-blue bg-clip-text text-transparent">
                        Biblical Tribes & Nations
                    </span>
                </h1>
                <p class="text-xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
                    Discover your covenant lineage through the comprehensive biblical genealogies. 
                    From the 12 Tribes of Israel to the witness nations of Edom, Is<PERSON><PERSON>, and the sons of <PERSON>.
                </p>
            </div>
        </div>
    </div>

    <!-- 12 Tribes of Israel Section -->
    <div class="py-16 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-orbitron font-bold text-cyber-cyan mb-4">
                    The 12 Tribes of Israel
                </h2>
                <p class="text-lg text-text-secondary mb-8">
                    The covenant nations - children of Jacob/Israel requiring Gate Keeper verification
                </p>
                <a href="{{ url_for('tribes.israel_tribes') }}" 
                   class="glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                    📜 Learn More About Israel
                </a>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8">
                {% for nation in covenant_nations %}
                <div class="glass-card p-6 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="text-4xl mb-3">{{ nation.flag_symbol }}</div>
                    <h3 class="font-orbitron font-bold text-cyber-cyan mb-2">{{ nation.nation_name }}</h3>
                    <p class="text-sm text-text-secondary mb-3">{{ nation.tribe_name }}</p>
                    <p class="text-xs text-text-tertiary">{{ nation.description[:50] }}...</p>
                    <div class="mt-3 text-xs text-cyber-green">
                        ⚖️ Gate Keeper Verification Required
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- 12 Dukes of Edom Section -->
    <div class="py-16 relative bg-gradient-to-r from-cyber-purple/10 to-transparent">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-orbitron font-bold text-cyber-purple mb-4">
                    The 12 Dukes of Edom
                </h2>
                <p class="text-lg text-text-secondary mb-8">
                    Descendants of Esau - witness nation with immediate inscription (Genesis 36:40-43)
                </p>
                <a href="{{ url_for('tribes.edom_dukes') }}" 
                   class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105"
                   style="border: 2px solid var(--cyber-purple);">
                    🏔️ Learn More About Edom
                </a>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8">
                {% for duke in edom_dukes %}
                <div class="glass-card p-6 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="text-4xl mb-3">{{ duke.flag_symbol }}</div>
                    <h3 class="font-orbitron font-bold text-cyber-purple mb-2">{{ duke.nation_name }}</h3>
                    <p class="text-sm text-text-secondary mb-3">{{ duke.tribe_name }}</p>
                    <p class="text-xs text-text-tertiary">{{ duke.description[:50] }}...</p>
                    <div class="mt-3 text-xs text-cyber-blue">
                        ⚡ Immediate Inscription
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- 12 Princes of Ishmael Section -->
    <div class="py-16 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-orbitron font-bold text-cyber-blue mb-4">
                    The 12 Princes of Ishmael
                </h2>
                <p class="text-lg text-text-secondary mb-8">
                    Sons of Abraham through Hagar - witness nation with immediate inscription (Genesis 25:13-15)
                </p>
                <a href="{{ url_for('tribes.ishmael_princes') }}" 
                   class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105"
                   style="border: 2px solid var(--cyber-blue);">
                    🏜️ Learn More About Ishmael
                </a>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8">
                {% for prince in ishmael_princes %}
                <div class="glass-card p-6 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="text-4xl mb-3">{{ prince.flag_symbol }}</div>
                    <h3 class="font-orbitron font-bold text-cyber-blue mb-2">{{ prince.nation_name }}</h3>
                    <p class="text-sm text-text-secondary mb-3">{{ prince.tribe_name }}</p>
                    <p class="text-xs text-text-tertiary">{{ prince.description[:50] }}...</p>
                    <div class="mt-3 text-xs text-cyber-blue">
                        ⚡ Immediate Inscription
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Hamitic Nations Section -->
    {% if hamitic_nations %}
    <div class="py-16 relative bg-gradient-to-r from-cyber-green/10 to-transparent">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-orbitron font-bold text-cyber-green mb-4">
                    Hamitic Nations
                </h2>
                <p class="text-lg text-text-secondary mb-8">
                    Sons of Ham - witness nations with immediate inscription (Genesis 10:6)
                </p>
                <a href="{{ url_for('tribes.hamitic_nations') }}" 
                   class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105"
                   style="border: 2px solid var(--cyber-green);">
                    🏺 Learn More About Ham's Descendants
                </a>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {% for nation in hamitic_nations %}
                <div class="glass-card p-6 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="text-4xl mb-3">{{ nation.flag_symbol }}</div>
                    <h3 class="font-orbitron font-bold text-cyber-green mb-2">{{ nation.nation_name }}</h3>
                    <p class="text-sm text-text-secondary mb-3">{{ nation.tribe_name }}</p>
                    <p class="text-xs text-text-tertiary">{{ nation.description[:50] }}...</p>
                    <div class="mt-3 text-xs text-cyber-blue">
                        ⚡ Immediate Inscription
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Registration Call to Action -->
    <div class="py-20 relative">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="glass-card-premium p-12">
                <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">
                    Ready to Discover Your Lineage?
                </h2>
                <p class="text-lg text-text-secondary mb-8 leading-relaxed">
                    Begin your covenant identity journey through Eden Mode. Discover your biblical heritage 
                    and join the appropriate tribal community based on your lineage.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ url_for('eden_mode.step1') }}"
                       class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-bold text-lg transition-all duration-300 hover:scale-105">
                        🌟 Begin Eden Mode Registration
                    </a>
                    <a href="/governance/public"
                       class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold text-lg transition-all duration-300 hover:scale-105">
                        ⚖️ View Gate Keeper Council
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
