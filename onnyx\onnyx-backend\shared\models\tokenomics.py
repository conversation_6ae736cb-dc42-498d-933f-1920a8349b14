"""
Biblical Tokenomics Module

This module implements the biblical economic principles for the Onnyx blockchain,
including jubilee resets, tiered mining rewards, gleaning pools, and anti-usury lending.
"""

import time
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone, timedelta

from shared.db.db import db
from shared.config.chain_parameters import chain_parameters

logger = logging.getLogger("onnyx.tokenomics")

class BiblicalTokenomics:
    """
    Implements biblical economic principles for the Onnyx blockchain.
    Enhanced for 24/6 mining schedule and covenant compliance.
    """

    def __init__(self):
        """Initialize the Biblical Tokenomics system."""
        self.gleaning_pool_id = "GLEANS_POOL"
        self.firstfruits_pool_id = "FIRSTFRUITS_POOL"
        self.community_pool_id = "COMMUNITY_POOL"

        # Enhanced Mining Constants (UPDATED MINING DECREE - DJM SON OF BENJAMIN)
        self.ANNUAL_MINING_CAP = 2200000.0  # 2.2M ONX per year
        self.DEV_ALLOCATION_ANNUAL = 100000.0  # 100K ONX for development (manual via council scroll)
        self.ACTIVE_MINING_DAYS = 313  # 365 - 52 Sabbaths
        self.DAILY_EMISSION = 7028.0  # ~7,028 ONX/day (decree specification)
        self.HOURLY_EMISSION = 293.0  # ~293 ONX/hour (decree specification)
        self.RIGHTEOUS_BONUS_RATE = 0.05  # +5% bonus for full Sabbath compliance (Mon-Wed)
        self.PENALTY_MULTIPLIER = 0.5  # 0.5× for Sabbath/Holy Day violations
        self.GLEANING_PERCENTAGE = 0.10  # 10% to gleaning pool

        # Tier Multipliers (EXACT MINING DECREE SPECIFICATIONS)
        self.TIER_MULTIPLIERS = {
            'citizen': 1.0,           # Verified identity
            'basic_sela': 2.5,        # Operates 1 selah (2×-3× range)
            'dual_sela': 5.5,         # Operates 2 selahs (4×-7× range)
            'triple_sela': 7.0,       # Operates 3 selahs (4×-7× range)
            'pro_validator': 9.0,     # ONNYX Pro Validator (8×-10× range)
            'penalty': 0.5            # Sabbath or Holy Day violation
        }

    def calculate_tiered_mining_reward(self, proposer_id: str, base_reward: float) -> Tuple[float, float]:
        """
        Calculate mining reward with deed-based multipliers and gleaning pool allocation.

        Args:
            proposer_id: Identity ID of the block proposer
            base_reward: Base mining reward amount

        Returns:
            Tuple of (effective_reward, gleaning_allocation)
        """
        try:
            # Get proposer's deed score
            deed_score = self._get_deed_score(proposer_id)

            # Calculate deed bonus (max 10% bonus)
            deed_multiplier = chain_parameters.get("deed_score_multiplier", 0.1)
            deed_bonus = min(deed_score * deed_multiplier, deed_multiplier)

            # Check for concentration penalty
            concentration_penalty = self._check_concentration_penalty(proposer_id)

            # Calculate effective reward
            effective_reward = base_reward * (1 + deed_bonus) * concentration_penalty

            # Apply min/max limits
            min_reward = chain_parameters.get("min_block_reward", 2)
            max_reward = chain_parameters.get("max_block_reward", 200)
            effective_reward = max(min_reward, min(max_reward, effective_reward))

            # Calculate gleaning pool allocation (2% of base reward)
            gleaning_percentage = chain_parameters.get("gleaning_pool_percentage", 0.02)
            gleaning_allocation = base_reward * gleaning_percentage

            logger.info(f"Calculated reward for {proposer_id}: base={base_reward}, "
                       f"deed_bonus={deed_bonus:.3f}, penalty={concentration_penalty:.3f}, "
                       f"effective={effective_reward:.3f}, gleaning={gleaning_allocation:.3f}")

            return effective_reward, gleaning_allocation

        except Exception as e:
            logger.error(f"Error calculating tiered mining reward: {e}")
            return base_reward, 0.0

    def _get_deed_score(self, identity_id: str) -> float:
        """Get the current deed score for an identity."""
        try:
            # Get deed score from identities table
            result = db.query_one(
                "SELECT deeds_score FROM identities WHERE identity_id = ?",
                (identity_id,)
            )
            return result["deeds_score"] if result else 0.0
        except Exception as e:
            logger.warning(f"Could not get deed score for {identity_id}: {e}")
            return 0.0

    def _check_concentration_penalty(self, identity_id: str) -> float:
        """Check if identity should receive concentration penalty."""
        try:
            # Get total balance for identity
            result = db.query_one("""
                SELECT SUM(balance) as total_balance
                FROM token_balances
                WHERE identity_id = ? AND token_id = 'ONX'
            """, (identity_id,))

            total_balance = result["total_balance"] if result and result["total_balance"] else 0
            concentration_threshold = chain_parameters.get("concentration_threshold", 1000000)

            if total_balance > concentration_threshold:
                penalty_rate = chain_parameters.get("concentration_penalty_rate", 0.1)
                logger.info(f"Applying concentration penalty to {identity_id}: "
                           f"balance={total_balance}, threshold={concentration_threshold}")
                return penalty_rate

            return 1.0

        except Exception as e:
            logger.warning(f"Could not check concentration penalty for {identity_id}: {e}")
            return 1.0

    def allocate_to_gleaning_pool(self, amount: float, token_id: str = "ONX") -> bool:
        """
        Allocate tokens to the gleaning pool.

        Args:
            amount: Amount to allocate
            token_id: Token ID (default: ONX)

        Returns:
            True if successful
        """
        try:
            current_time = int(time.time())

            # Update gleaning pool balance
            db.execute("""
                INSERT OR REPLACE INTO jubilee_pools (pool_id, pool_type, total_amount, token_id, created_at, last_distribution)
                VALUES (?, 'GLEANING', COALESCE((SELECT total_amount FROM jubilee_pools WHERE pool_id = ?), 0) + ?, ?, ?, ?)
            """, (self.gleaning_pool_id, self.gleaning_pool_id, amount, token_id, current_time, current_time))

            logger.info(f"Allocated {amount} {token_id} to gleaning pool")
            return True

        except Exception as e:
            logger.error(f"Error allocating to gleaning pool: {e}")
            return False

    def record_deed(self, identity_id: str, deed_type: str, deed_value: float,
                   description: str = "", block_height: int = None) -> bool:
        """
        Record a righteous deed for an identity.

        Args:
            identity_id: Identity ID
            deed_type: Type of deed (MUTUAL_AID, DONATION, FIRSTFRUITS, SABBATH_OBSERVANCE)
            deed_value: Value/score of the deed
            description: Optional description
            block_height: Current block height

        Returns:
            True if successful
        """
        try:
            current_time = int(time.time())

            # Record the deed
            db.execute("""
                INSERT INTO deeds_ledger (identity_id, deed_type, deed_value, description, timestamp, block_height)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (identity_id, deed_type, deed_value, description, current_time, block_height))

            # Update identity's deed score
            self._update_deed_score(identity_id, deed_value)

            logger.info(f"Recorded deed for {identity_id}: {deed_type} = {deed_value}")
            return True

        except Exception as e:
            logger.error(f"Error recording deed: {e}")
            return False

    def _update_deed_score(self, identity_id: str, deed_value: float) -> None:
        """Update an identity's deed score."""
        try:
            db.execute("""
                UPDATE identities
                SET deeds_score = COALESCE(deeds_score, 0) + ?,
                    updated_at = ?
                WHERE identity_id = ?
            """, (deed_value, int(time.time()), identity_id))
        except Exception as e:
            logger.warning(f"Could not update deed score for {identity_id}: {e}")

    def update_activity_tracking(self, identity_id: str, block_height: int) -> None:
        """
        Update activity tracking for an identity.

        Args:
            identity_id: Identity ID
            block_height: Current block height
        """
        try:
            current_time = int(time.time())

            db.execute("""
                UPDATE identities
                SET last_active_timestamp = ?,
                    last_transaction_height = ?,
                    updated_at = ?
                WHERE identity_id = ?
            """, (current_time, block_height, current_time, identity_id))

        except Exception as e:
            logger.warning(f"Could not update activity tracking for {identity_id}: {e}")

    def check_dormant_accounts(self, current_block_height: int) -> List[str]:
        """
        Check for dormant accounts that should be subject to jubilee reclamation.

        Args:
            current_block_height: Current blockchain height

        Returns:
            List of dormant identity IDs
        """
        try:
            dormancy_threshold = chain_parameters.get("dormancy_threshold_blocks", 7200)
            threshold_height = current_block_height - dormancy_threshold

            dormant_accounts = db.query("""
                SELECT identity_id, last_transaction_height, last_active_timestamp
                FROM identities
                WHERE last_transaction_height < ? AND last_transaction_height > 0
            """, (threshold_height,))

            dormant_ids = [acc["identity_id"] for acc in dormant_accounts]

            if dormant_ids:
                logger.info(f"Found {len(dormant_ids)} dormant accounts at block {current_block_height}")

            return dormant_ids

        except Exception as e:
            logger.error(f"Error checking dormant accounts: {e}")
            return []

    def is_sabbath_period(self, user_timezone: str = None) -> bool:
        """
        Check if current time is within Sabbath period.
        Enhanced for proper timezone handling.

        Args:
            user_timezone: User's timezone (e.g., 'America/Chicago', 'US/Central')
                          If None, uses Jerusalem time (UTC+2/+3)

        Returns:
            True if it's currently Sabbath
        """
        try:
            import pytz

            # Default to Jerusalem timezone if no user timezone provided
            if user_timezone is None:
                user_timezone = 'Asia/Jerusalem'

            # Get current time in user's timezone
            try:
                tz = pytz.timezone(user_timezone)
                now = datetime.now(tz)
            except:
                # Fallback to UTC if timezone is invalid
                logger.warning(f"Invalid timezone {user_timezone}, falling back to UTC")
                now = datetime.now(timezone.utc)

            # Get Sabbath configuration
            sabbath_start_day = chain_parameters.get("sabbath_start_day", 5)  # Friday
            sabbath_start_hour = chain_parameters.get("sabbath_start_hour", 18)  # 6 PM
            sabbath_duration = chain_parameters.get("sabbath_duration_hours", 25)  # 25 hours

            # Calculate if we're in Sabbath period
            current_day = now.weekday()  # 0=Monday, 6=Sunday
            current_hour = now.hour

            # Friday evening start
            if current_day == sabbath_start_day and current_hour >= sabbath_start_hour:
                return True
            # Saturday continuation until end
            elif current_day == (sabbath_start_day + 1) % 7 and current_hour < (sabbath_start_hour + sabbath_duration - 24):
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking Sabbath period: {e}")
            return False

    def is_mining_allowed(self) -> bool:
        """
        Enhanced mining schedule enforcement for 24/6 system.
        Checks Sabbath, New Moons, and Feast Days.

        Returns:
            bool: True if mining is currently allowed
        """
        try:
            # Check basic Sabbath period
            if self.is_sabbath_period():
                return False

            # Check for New Moon periods (first day of Hebrew month)
            if self.is_new_moon_period():
                return False

            # Check for biblical feast days
            if self.is_feast_day():
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking mining allowance: {e}")
            return True  # Default to allowing mining on error

    def is_new_moon_period(self) -> bool:
        """
        Check if current time is during a New Moon rest period.

        Returns:
            bool: True if it's a New Moon period
        """
        try:
            # Simplified implementation - check first day of month
            # In production, this would use proper Hebrew calendar calculations
            now = datetime.now(timezone.utc)
            return now.day == 1

        except Exception as e:
            logger.error(f"Error checking New Moon period: {e}")
            return False

    def is_feast_day(self) -> bool:
        """
        Check if current time is during a biblical feast day.

        Returns:
            bool: True if it's a feast day
        """
        try:
            # Simplified implementation for major feast days
            # In production, this would use proper Hebrew calendar calculations
            now = datetime.now(timezone.utc)

            # Example feast days (would need proper Hebrew calendar integration)
            feast_days = [
                # Passover (typically March/April)
                (3, 15), (3, 16), (3, 21), (3, 22),
                # Shavuot (typically May/June)
                (5, 15),
                # Rosh Hashanah (typically September)
                (9, 1), (9, 2),
                # Yom Kippur (typically September)
                (9, 10),
                # Sukkot (typically September/October)
                (9, 15), (9, 16), (9, 21), (9, 22)
            ]

            return (now.month, now.day) in feast_days

        except Exception as e:
            logger.error(f"Error checking feast day: {e}")
            return False

    def calculate_enhanced_mining_reward(self, proposer_id: str, base_reward: float) -> tuple:
        """
        Enhanced mining reward calculation with tier multipliers and righteous bonuses.
        Implements the UPDATED MINING DECREE specifications.

        Args:
            proposer_id: Identity ID of the block proposer
            base_reward: Base mining reward amount

        Returns:
            Tuple of (effective_reward, gleaning_allocation, bonus_applied)
        """
        try:
            # Get proposer's tier multiplier based on Sela ownership
            tier_multiplier = self._get_tier_multiplier(proposer_id)

            # Check for righteous bonus eligibility
            righteous_bonus = self._check_righteous_bonus(proposer_id)

            # Calculate base reward with tier multiplier
            tiered_reward = base_reward * tier_multiplier

            # Apply righteous bonus if eligible
            if righteous_bonus:
                tiered_reward *= (1 + self.RIGHTEOUS_BONUS_RATE)

            # Check for penalties (Sabbath violations, etc.)
            penalty_multiplier = self._check_penalty_status(proposer_id)
            tiered_reward *= penalty_multiplier

            # Apply annual cap enforcement
            tiered_reward = self._enforce_annual_cap(tiered_reward)

            # Calculate gleaning pool allocation (10% of base reward)
            gleaning_allocation = base_reward * self.GLEANING_PERCENTAGE

            # Apply min/max limits
            min_reward = chain_parameters.get("min_block_reward", 2)
            max_reward = chain_parameters.get("max_block_reward", 200)
            effective_reward = max(min_reward, min(max_reward, tiered_reward))

            logger.info(f"Enhanced mining reward for {proposer_id}: {effective_reward} ONX "
                       f"(tier: {tier_multiplier}×, righteous: {righteous_bonus}, penalty: {penalty_multiplier}×)")

            return effective_reward, gleaning_allocation, righteous_bonus

        except Exception as e:
            logger.error(f"Error calculating enhanced mining reward: {e}")
            return base_reward, 0.0, False

    # Feature 4: Anti-Usury Lending System

    def create_loan(self, lender_id: str, borrower_id: str, amount: float,
                   token_id: str = "ONX", grace_blocks: int = None) -> str:
        """
        Create a new interest-free loan.

        Args:
            lender_id: Identity ID of the lender
            borrower_id: Identity ID of the borrower
            amount: Loan amount
            token_id: Token ID (default: ONX)
            grace_blocks: Grace period in blocks

        Returns:
            Loan ID if successful
        """
        try:
            current_time = int(time.time())
            grace_blocks = grace_blocks or chain_parameters.get("loan_grace_blocks", 14400)
            loan_id = f"loan_{current_time}_{lender_id}_{borrower_id}"

            # Create loan record
            db.execute("""
                INSERT INTO loans (loan_id, lender_id, borrower_id, amount, token_id,
                                 grace_blocks, forgiveness_threshold, created_at, created_block)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (loan_id, lender_id, borrower_id, amount, token_id, grace_blocks,
                  chain_parameters.get("loan_forgiveness_threshold", 0.8), current_time, 0))

            # Transfer tokens from lender to borrower
            self._transfer_tokens(lender_id, borrower_id, amount, token_id)

            # Record deed for lender
            self.record_deed(lender_id, "LENDING", amount * 0.1, f"Provided loan of {amount} {token_id}")

            logger.info(f"Created loan {loan_id}: {amount} {token_id} from {lender_id} to {borrower_id}")
            return loan_id

        except Exception as e:
            logger.error(f"Error creating loan: {e}")
            return ""

    def repay_loan(self, loan_id: str, borrower_id: str, amount: float) -> bool:
        """
        Make a loan repayment.

        Args:
            loan_id: Loan ID
            borrower_id: Identity ID of the borrower
            amount: Repayment amount

        Returns:
            True if successful
        """
        try:
            # Get loan details
            loan = db.query_one("SELECT * FROM loans WHERE loan_id = ? AND status = 'ACTIVE'", (loan_id,))
            if not loan:
                logger.error(f"Loan {loan_id} not found or not active")
                return False

            if loan["borrower_id"] != borrower_id:
                logger.error(f"Borrower mismatch for loan {loan_id}")
                return False

            # Update loan payment
            new_amount_paid = loan["amount_paid"] + amount
            current_time = int(time.time())

            # Check if loan is fully repaid
            if new_amount_paid >= loan["amount"]:
                status = "REPAID"
                new_amount_paid = loan["amount"]  # Cap at loan amount
            else:
                status = "ACTIVE"

            db.execute("""
                UPDATE loans
                SET amount_paid = ?, status = ?, updated_at = ?
                WHERE loan_id = ?
            """, (new_amount_paid, status, current_time, loan_id))

            # Transfer repayment to lender
            self._transfer_tokens(borrower_id, loan["lender_id"], amount, loan["token_id"])

            # Record deed for borrower
            self.record_deed(borrower_id, "LOAN_REPAYMENT", amount * 0.05, f"Repaid {amount} on loan {loan_id}")

            logger.info(f"Loan repayment: {amount} on {loan_id}, total paid: {new_amount_paid}/{loan['amount']}")
            return True

        except Exception as e:
            logger.error(f"Error processing loan repayment: {e}")
            return False

    def check_loan_forgiveness(self, current_block: int) -> List[str]:
        """
        Check for loans eligible for automatic forgiveness.

        Args:
            current_block: Current block height

        Returns:
            List of forgiven loan IDs
        """
        try:
            forgiven_loans = []

            # Get active loans
            loans = db.query("SELECT * FROM loans WHERE status = 'ACTIVE'")

            for loan in loans:
                # Check if grace period has expired and threshold met
                blocks_elapsed = current_block - loan["created_block"]
                payment_ratio = loan["amount_paid"] / loan["amount"] if loan["amount"] > 0 else 0

                if (blocks_elapsed >= loan["grace_blocks"] and
                    payment_ratio >= loan["forgiveness_threshold"]):

                    # Forgive the loan
                    self.forgive_loan(loan["loan_id"], "SYSTEM_AUTO")
                    forgiven_loans.append(loan["loan_id"])

            if forgiven_loans:
                logger.info(f"Auto-forgave {len(forgiven_loans)} loans at block {current_block}")

            return forgiven_loans

        except Exception as e:
            logger.error(f"Error checking loan forgiveness: {e}")
            return []

    def forgive_loan(self, loan_id: str, forgiver_id: str) -> bool:
        """
        Forgive a loan (cancel remaining debt).

        Args:
            loan_id: Loan ID
            forgiver_id: Identity ID of forgiver (lender or SYSTEM_AUTO)

        Returns:
            True if successful
        """
        try:
            current_time = int(time.time())

            # Update loan status
            db.execute("""
                UPDATE loans
                SET status = 'FORGIVEN', updated_at = ?
                WHERE loan_id = ? AND status = 'ACTIVE'
            """, (current_time, loan_id))

            # Get loan details for deed recording
            loan = db.query_one("SELECT * FROM loans WHERE loan_id = ?", (loan_id,))
            if loan:
                remaining_debt = loan["amount"] - loan["amount_paid"]

                # Record deed for forgiveness
                if forgiver_id != "SYSTEM_AUTO":
                    self.record_deed(forgiver_id, "DEBT_FORGIVENESS", remaining_debt * 0.2,
                                   f"Forgave debt of {remaining_debt} on loan {loan_id}")

                # Record deed for borrower (debt relief)
                self.record_deed(loan["borrower_id"], "DEBT_RELIEF", remaining_debt * 0.1,
                               f"Received debt forgiveness of {remaining_debt} on loan {loan_id}")

            logger.info(f"Forgave loan {loan_id} by {forgiver_id}")
            return True

        except Exception as e:
            logger.error(f"Error forgiving loan: {e}")
            return False

    # Feature 5: Firstfruits Offering Mechanism

    def make_firstfruits_offering(self, offerer_id: str, amount: float, token_id: str = "ONX") -> bool:
        """
        Make a firstfruits offering to the community.

        Args:
            offerer_id: Identity ID of the offerer
            amount: Offering amount
            token_id: Token ID (default: ONX)

        Returns:
            True if successful
        """
        try:
            current_time = int(time.time())

            # Transfer offering to firstfruits pool
            self._transfer_tokens(offerer_id, "FIRSTFRUITS_POOL", amount, token_id)

            # Add to firstfruits pool
            db.execute("""
                INSERT OR REPLACE INTO jubilee_pools (pool_id, pool_type, total_amount, token_id, created_at, last_distribution)
                VALUES ('FIRSTFRUITS_POOL', 'FIRSTFRUITS',
                        COALESCE((SELECT total_amount FROM jubilee_pools WHERE pool_id = 'FIRSTFRUITS_POOL'), 0) + ?,
                        ?, ?, ?)
            """, (amount, token_id, current_time, current_time))

            # Record the deed
            self.record_deed(offerer_id, "FIRSTFRUITS", amount, f"Firstfruits offering of {amount} {token_id}")

            # Award Etzem tokens
            etzem_reward = chain_parameters.get("firstfruits_etzem_reward", 2)
            self._mint_tokens(offerer_id, etzem_reward, "ETZEM")

            # Assign Etzem token class if not already assigned
            self.assign_token_class("ETZEM", "Etzem")

            logger.info(f"Firstfruits offering: {amount} {token_id} from {offerer_id}, awarded {etzem_reward} ETZEM")
            return True

        except Exception as e:
            logger.error(f"Error processing firstfruits offering: {e}")
            return False

    # Feature 6: Anti-Concentration Protocol

    def enforce_concentration_limits(self, identity_id: str, current_block: int) -> bool:
        """
        Enforce anti-concentration protocol for an identity.

        Args:
            identity_id: Identity ID to check
            current_block: Current block height

        Returns:
            True if concentration limits enforced
        """
        try:
            concentration_threshold = chain_parameters.get("concentration_threshold", 1000000)

            # Get total balance across all tokens
            total_balance = self._get_total_balance(identity_id)

            if total_balance > concentration_threshold:
                # Apply concentration penalty
                penalty_rate = chain_parameters.get("concentration_penalty_rate", 0.1)

                # Record concentration violation
                self.record_deed(identity_id, "CONCENTRATION_VIOLATION", -total_balance * 0.01,
                               f"Exceeded concentration threshold: {total_balance}")

                # Suggest redistribution to gleaning pool
                excess_amount = total_balance - concentration_threshold
                suggested_donation = excess_amount * 0.1  # Suggest 10% of excess

                logger.warning(f"Concentration limit exceeded by {identity_id}: {total_balance} > {concentration_threshold}")
                logger.info(f"Suggested gleaning pool donation: {suggested_donation}")

                return True

            return False

        except Exception as e:
            logger.error(f"Error enforcing concentration limits: {e}")
            return False

    # Feature 7: Biblical Token Classification System

    def assign_token_class(self, token_id: str, class_type: str, metadata: str = "{}") -> bool:
        """
        Assign a biblical class to a token.

        Args:
            token_id: Token ID
            class_type: Class type (Avodah, Zedek, Yovel, Etzem)
            metadata: Additional metadata

        Returns:
            True if successful
        """
        try:
            valid_classes = ["Avodah", "Zedek", "Yovel", "Etzem"]
            if class_type not in valid_classes:
                logger.error(f"Invalid token class: {class_type}")
                return False

            current_time = int(time.time())

            db.execute("""
                INSERT OR REPLACE INTO token_classes (token_id, class_type, class_metadata, assigned_at)
                VALUES (?, ?, ?, ?)
            """, (token_id, class_type, metadata, current_time))

            logger.info(f"Assigned token class {class_type} to {token_id}")
            return True

        except Exception as e:
            logger.error(f"Error assigning token class: {e}")
            return False

    def get_token_class(self, token_id: str) -> str:
        """
        Get the biblical class of a token.

        Args:
            token_id: Token ID

        Returns:
            Token class or empty string if not classified
        """
        try:
            result = db.query_one("SELECT class_type FROM token_classes WHERE token_id = ?", (token_id,))
            return result["class_type"] if result else ""
        except Exception as e:
            logger.error(f"Error getting token class: {e}")
            return ""

    def classify_token_by_purpose(self, token_id: str, purpose: str) -> bool:
        """
        Automatically classify a token based on its purpose.

        Args:
            token_id: Token ID
            purpose: Token purpose (labor, righteousness, jubilee, essence)

        Returns:
            True if classified successfully
        """
        try:
            purpose_to_class = {
                "labor": "Avodah",
                "work": "Avodah",
                "service": "Avodah",
                "righteousness": "Zedek",
                "justice": "Zedek",
                "charity": "Zedek",
                "jubilee": "Yovel",
                "redistribution": "Yovel",
                "reset": "Yovel",
                "essence": "Etzem",
                "core": "Etzem",
                "fundamental": "Etzem"
            }

            class_type = purpose_to_class.get(purpose.lower())
            if class_type:
                return self.assign_token_class(token_id, class_type, f'{{"purpose": "{purpose}"}}')

            logger.warning(f"Unknown purpose for token classification: {purpose}")
            return False

        except Exception as e:
            logger.error(f"Error classifying token by purpose: {e}")
            return False

    # Feature 8: Minimum/Maximum Wage Logic (already implemented in calculate_tiered_mining_reward)

    def enforce_reward_bounds(self, calculated_reward: float) -> float:
        """
        Enforce minimum and maximum reward bounds.

        Args:
            calculated_reward: Calculated reward amount

        Returns:
            Bounded reward amount
        """
        min_reward = chain_parameters.get("min_block_reward", 2)
        max_reward = chain_parameters.get("max_block_reward", 200)

        bounded_reward = max(min_reward, min(max_reward, calculated_reward))

        if bounded_reward != calculated_reward:
            logger.info(f"Reward bounded: {calculated_reward} -> {bounded_reward}")

        return bounded_reward

    # Feature 9: Sabbath Enforcement and Righteous Boost

    def record_sabbath_observance(self, identity_id: str, block_height: int) -> bool:
        """
        Record Sabbath observance for an identity.

        Args:
            identity_id: Identity ID
            block_height: Current block height

        Returns:
            True if recorded successfully
        """
        try:
            # Award sabbath deed bonus
            sabbath_bonus = chain_parameters.get("sabbath_deed_bonus", 0.2)

            self.record_deed(identity_id, "SABBATH_OBSERVANCE", sabbath_bonus,
                           f"Observed Sabbath at block {block_height}")

            # Mark as sabbath observer
            db.execute("""
                UPDATE identities
                SET sabbath_observer = 1, updated_at = ?
                WHERE identity_id = ?
            """, (int(time.time()), identity_id))

            logger.info(f"Recorded Sabbath observance for {identity_id}")
            return True

        except Exception as e:
            logger.error(f"Error recording Sabbath observance: {e}")
            return False

    def get_sabbath_observers(self) -> List[str]:
        """
        Get list of current Sabbath observers.

        Returns:
            List of identity IDs who observe Sabbath
        """
        try:
            observers = db.query("SELECT identity_id FROM identities WHERE sabbath_observer = 1")
            return [obs["identity_id"] for obs in observers]
        except Exception as e:
            logger.error(f"Error getting Sabbath observers: {e}")
            return []

    def start_sabbath_period(self, block_height: int) -> bool:
        """
        Start a new Sabbath period.

        Args:
            block_height: Current block height

        Returns:
            True if started successfully
        """
        try:
            current_time = int(time.time())
            sabbath_duration = chain_parameters.get("sabbath_duration_hours", 25)
            end_time = current_time + (sabbath_duration * 3600)  # Convert hours to seconds

            db.execute("""
                INSERT INTO sabbath_periods (start_timestamp, end_timestamp, block_height_start)
                VALUES (?, ?, ?)
            """, (current_time, end_time, block_height))

            logger.info(f"Started Sabbath period at block {block_height}")
            return True

        except Exception as e:
            logger.error(f"Error starting Sabbath period: {e}")
            return False

    # Enhanced Mining Helper Methods

    def _get_tier_multiplier(self, identity_id: str) -> float:
        """
        Get tier multiplier based on Sela ownership and status.
        UPDATED TO MATCH MINING DECREE SPECIFICATIONS

        Args:
            identity_id: Identity ID

        Returns:
            Tier multiplier (1.0 to 9.0) - EXACT DECREE COMPLIANCE
        """
        try:
            # Get Sela count for this identity
            sela_count = db.query_one("""
                SELECT COUNT(*) as count FROM selas
                WHERE identity_id = ? AND status = 'active'
            """, (identity_id,))

            sela_count = sela_count['count'] if sela_count else 0

            # Check for ONNYX Pro Validator status (8×-10× range, using 9×)
            pro_validator = db.query_one("""
                SELECT mining_tier FROM selas
                WHERE identity_id = ? AND mining_tier = 'pro' AND status = 'active'
            """, (identity_id,))

            if pro_validator:
                return self.TIER_MULTIPLIERS['pro_validator']  # 9.0× (ONNYX Pro Validator)
            elif sela_count >= 3:
                return self.TIER_MULTIPLIERS['triple_sela']    # 7.0× (3+ Selahs)
            elif sela_count >= 2:
                return self.TIER_MULTIPLIERS['dual_sela']      # 5.5× (2 Selahs)
            elif sela_count >= 1:
                return self.TIER_MULTIPLIERS['basic_sela']     # 2.5× (1 Selah)
            else:
                return self.TIER_MULTIPLIERS['citizen']        # 1.0× (Verified identity)

        except Exception as e:
            logger.error(f"Error getting tier multiplier for {identity_id}: {e}")
            return 1.0

    def _check_righteous_bonus(self, identity_id: str) -> bool:
        """
        Check if identity is eligible for righteous bonus (+5%).
        Applied Mon-Wed for full Sabbath compliance.

        Args:
            identity_id: Identity ID

        Returns:
            True if eligible for righteous bonus
        """
        try:
            now = datetime.now(timezone.utc)

            # Only apply bonus Monday-Wednesday (0-2)
            if now.weekday() not in [0, 1, 2]:
                return False

            # Check Sabbath compliance for previous week
            week_ago = now - timedelta(days=7)

            # Check for any Sabbath violations in the past week
            violations = db.query_one("""
                SELECT COUNT(*) as count FROM deeds_ledger
                WHERE identity_id = ?
                AND deed_type = 'SABBATH_VIOLATION'
                AND timestamp > ?
            """, (identity_id, int(week_ago.timestamp())))

            violation_count = violations['count'] if violations else 0

            # Also check for positive Sabbath observance
            observances = db.query_one("""
                SELECT COUNT(*) as count FROM deeds_ledger
                WHERE identity_id = ?
                AND deed_type = 'SABBATH_OBSERVANCE'
                AND timestamp > ?
            """, (identity_id, int(week_ago.timestamp())))

            observance_count = observances['count'] if observances else 0

            # Eligible if no violations and at least one observance
            return violation_count == 0 and observance_count > 0

        except Exception as e:
            logger.error(f"Error checking righteous bonus for {identity_id}: {e}")
            return False

    def _check_penalty_status(self, identity_id: str) -> float:
        """
        Check for penalty status (Sabbath/Holy Day violations).

        Args:
            identity_id: Identity ID

        Returns:
            Penalty multiplier (0.5 for violations, 1.0 for clean record)
        """
        try:
            # Check for recent violations (past 30 days)
            thirty_days_ago = datetime.now(timezone.utc) - timedelta(days=30)

            violations = db.query_one("""
                SELECT COUNT(*) as count FROM deeds_ledger
                WHERE identity_id = ?
                AND deed_type IN ('SABBATH_VIOLATION', 'FEAST_VIOLATION', 'NEW_MOON_VIOLATION')
                AND timestamp > ?
            """, (identity_id, int(thirty_days_ago.timestamp())))

            violation_count = violations['count'] if violations else 0

            if violation_count > 0:
                logger.warning(f"Penalty applied to {identity_id}: {violation_count} recent violations")
                return self.PENALTY_MULTIPLIER

            return 1.0

        except Exception as e:
            logger.error(f"Error checking penalty status for {identity_id}: {e}")
            return 1.0

    def _enforce_annual_cap(self, reward_amount: float) -> float:
        """
        Enforce annual mining cap (2.2M ONX).

        Args:
            reward_amount: Proposed reward amount

        Returns:
            Capped reward amount
        """
        try:
            # Get current year's total emissions
            current_year = datetime.now(timezone.utc).year
            year_start = datetime(current_year, 1, 1, tzinfo=timezone.utc)

            # Try enhanced_mining_rewards first, fall back to mining_rewards
            year_emissions = db.query_one("""
                SELECT COALESCE(SUM(reward_amount), 0) as total
                FROM enhanced_mining_rewards
                WHERE created_at > ?
            """, (int(year_start.timestamp()),))

            if not year_emissions or year_emissions['total'] == 0:
                # Fall back to original mining_rewards table if it exists
                try:
                    year_emissions = db.query_one("""
                        SELECT COALESCE(SUM(amount), 0) as total
                        FROM mining_rewards
                        WHERE timestamp > ?
                    """, (int(year_start.timestamp()),))
                except:
                    year_emissions = {'total': 0.0}

            total_emitted = year_emissions['total'] if year_emissions else 0.0

            # Check if adding this reward would exceed cap
            if total_emitted + reward_amount > self.ANNUAL_MINING_CAP:
                remaining_cap = max(0, self.ANNUAL_MINING_CAP - total_emitted)
                logger.warning(f"Annual cap enforcement: {reward_amount} -> {remaining_cap}")
                return remaining_cap

            return reward_amount

        except Exception as e:
            logger.error(f"Error enforcing annual cap: {e}")
            return reward_amount

    # Helper methods

    def _transfer_tokens(self, from_id: str, to_id: str, amount: float, token_id: str) -> bool:
        """Transfer tokens between identities."""
        try:
            current_time = int(time.time())

            # Deduct from sender
            db.execute("""
                UPDATE token_balances
                SET balance = balance - ?, updated_at = ?
                WHERE identity_id = ? AND token_id = ?
            """, (amount, current_time, from_id, token_id))

            # Credit to receiver
            db.execute("""
                INSERT OR REPLACE INTO token_balances (identity_id, token_id, balance, updated_at)
                VALUES (?, ?, COALESCE((SELECT balance FROM token_balances WHERE identity_id = ? AND token_id = ?), 0) + ?, ?)
            """, (to_id, token_id, to_id, token_id, amount, current_time))

            return True
        except Exception as e:
            logger.error(f"Error transferring tokens: {e}")
            return False

    def _mint_tokens(self, to_id: str, amount: float, token_id: str) -> bool:
        """Mint new tokens to an identity."""
        try:
            current_time = int(time.time())

            db.execute("""
                INSERT OR REPLACE INTO token_balances (identity_id, token_id, balance, updated_at)
                VALUES (?, ?, COALESCE((SELECT balance FROM token_balances WHERE identity_id = ? AND token_id = ?), 0) + ?, ?)
            """, (to_id, token_id, to_id, token_id, amount, current_time))

            return True
        except Exception as e:
            logger.error(f"Error minting tokens: {e}")
            return False

    def _get_total_balance(self, identity_id: str) -> float:
        """Get total balance across all tokens for an identity."""
        try:
            result = db.query_one("""
                SELECT SUM(balance) as total_balance
                FROM token_balances
                WHERE identity_id = ?
            """, (identity_id,))

            return result["total_balance"] if result and result["total_balance"] else 0.0
        except Exception as e:
            logger.error(f"Error getting total balance: {e}")
            return 0.0

    def _get_deed_score(self, identity_id: str) -> float:
        """Get current deed score for an identity."""
        try:
            result = db.query_one("""
                SELECT SUM(deed_value) as total_score
                FROM deeds_ledger
                WHERE identity_id = ?
            """, (identity_id,))

            return result["total_score"] if result and result["total_score"] else 0.0
        except Exception as e:
            logger.error(f"Error getting deed score: {e}")
            return 0.0

    def _check_concentration_penalty(self, identity_id: str) -> float:
        """Check for concentration penalty multiplier."""
        try:
            concentration_threshold = chain_parameters.get("concentration_threshold", 1000000)
            total_balance = self._get_total_balance(identity_id)

            if total_balance > concentration_threshold:
                penalty_rate = chain_parameters.get("concentration_penalty_rate", 0.1)
                return 1.0 - penalty_rate

            return 1.0
        except Exception as e:
            logger.error(f"Error checking concentration penalty: {e}")
            return 1.0

# Global instance
biblical_tokenomics = BiblicalTokenomics()
