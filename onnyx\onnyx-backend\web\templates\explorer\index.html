{% extends "base.html" %}

{% block title %}Blockchain Explorer - ONNYX Platform{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/live-explorer.css') }}">
{% endblock %}

{% block content %}
<div class="explorer-content hero-gradient cyber-grid relative py-8">
    <!-- Floating particles with precise positioning -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full animate-ping opacity-70"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full animate-pulse opacity-60"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce opacity-50"></div>
    </div>

    <div class="container-xl px-4 relative z-10">
        <!-- Precision Header Section -->
        <div class="text-center mb-12">
            <!-- ONNYX Logo with precise measurements -->
            <div class="mb-8 flex justify-center">
                <div class="flex items-center justify-center group">
                    <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                         alt="ONNYX Logo"
                         class="onnyx-page-logo w-16 h-16 md:w-20 md:h-20 object-contain"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <!-- Fallback symbol only if image fails -->
                    <span class="text-6xl font-black text-cyber-cyan" style="display: none;">⬢</span>
                </div>
            </div>

            <h1 class="text-5xl md:text-7xl font-orbitron font-bold mb-6">
                <span class="hologram-text">Blockchain Explorer</span>
            </h1>
            <p class="text-xl md:text-2xl text-secondary container-lg mx-auto leading-relaxed mb-8">
                Real-time exploration of the ONNYX blockchain network and transaction history
            </p>

            <!-- Precision Network Status -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-8">
                <div class="glass-card text-center p-4 hover:scale-105 transition-normal">
                    <div class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2"
                         id="latest-block"
                         data-format="number"
                         data-value="{{ latest_block or 0 }}">{{ latest_block or 0 }}</div>
                    <div class="text-sm text-tertiary uppercase tracking-wider">Latest Block</div>
                </div>
                <div class="glass-card text-center p-4 hover:scale-105 transition-normal">
                    <div class="text-3xl font-orbitron font-bold text-cyber-purple mb-2"
                         id="total-transactions"
                         data-format="number"
                         data-value="{{ total_transactions or 0 }}"
                         data-compact="true">{{ total_transactions or 0 }}</div>
                    <div class="text-sm text-tertiary uppercase tracking-wider">Total Transactions</div>
                </div>
                <div class="glass-card text-center p-4 hover:scale-105 transition-normal">
                    <div class="text-3xl font-orbitron font-bold text-cyber-blue mb-2"
                         id="network-hashrate"
                         data-value="{{ network_hashrate or 'N/A' }}">{{ network_hashrate or 'N/A' }}</div>
                    <div class="text-sm text-tertiary uppercase tracking-wider">Network Hashrate</div>
                </div>
                <div class="glass-card text-center p-4 hover:scale-105 transition-normal">
                    <div class="flex items-center justify-center mb-2">
                        <div class="w-3 h-3 bg-cyber-green rounded-full animate-pulse mr-2 shadow-sm shadow-cyber-green/50"></div>
                        <span class="text-3xl font-orbitron font-bold text-cyber-green">LIVE</span>
                    </div>
                    <div class="text-sm text-tertiary uppercase tracking-wider">Network Status</div>
                </div>
            </div>
        </div>

        <!-- Precision Search Section -->
        <div class="glass-card mb-8">
            <div class="card-header">
                <h2 class="card-title">🔍 Search Blockchain</h2>
                <p class="card-subtitle">Search by block hash, transaction ID, or address</p>
            </div>
            <div class="card-body">
                <div class="flex flex-col lg:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <input type="text"
                                   id="blockchain-search"
                                   placeholder="Search by block hash, transaction ID, or address..."
                                   class="w-full pl-12 pr-4 py-3 rounded-lg border border-glass-border bg-glass-bg backdrop-blur-md text-text-primary placeholder-text-muted focus:border-cyber-cyan focus:ring-2 focus:ring-cyber-cyan/20 transition-all duration-300 hover:bg-glass-hover">
                            <svg class="w-5 h-5 text-cyber-cyan absolute left-4 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <button onclick="performSearch()"
                            class="glass-button-primary px-8 py-3 rounded-lg font-orbitron font-bold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-cyber-cyan/20 min-h-[44px] flex items-center justify-center">
                        <span class="mr-2">🔍</span>
                        Search
                    </button>
                </div>
                <!-- Search Results Container -->
                <div id="search-results" class="mt-6 hidden">
                    <div class="glass-card p-4">
                        <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-4">Search Results</h3>
                        <div id="search-results-content">
                            <!-- Results will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Precision Recent Blocks Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-8">
            <!-- Recent Blocks -->
            <div class="glass-card">
                <div class="card-header">
                    <div class="flex items-center justify-between">
                        <h2 class="card-title">⛓️ Recent Blocks</h2>
                        <div class="flex items-center gap-2">
                            <button class="refresh-button glass-button-sm px-3 py-1 text-xs min-h-[44px]" data-refresh="blocks" title="Refresh Blocks">
                                🔄
                            </button>
                            <a href="{{ url_for('explorer.blocks') }}" class="text-cyber-cyan hover:text-primary transition-normal text-sm">
                                View All →
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">

                    <div class="space-y-3" id="recent-blocks-container">
                        {% if recent_blocks %}
                            {% for block in recent_blocks[:5] %}
                            <div class="glass-card p-4 hover:bg-glass-hover transition-normal">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="flex items-center space-x-3 mb-2">
                                            <span class="text-lg font-orbitron font-bold text-primary">Block #{{ block.block_height }}</span>
                                            <span class="px-2 py-1 bg-cyber-green/20 text-cyber-green text-xs rounded-md font-medium">CONFIRMED</span>
                                        </div>
                                        <div class="text-sm text-muted space-y-1">
                                            <div>Hash: <span class="text-cyber-cyan font-mono text-xs"
                                                             data-format="hash"
                                                             data-value="{{ block.block_hash }}">{{ block.block_hash[:16] }}...</span></div>
                                            <div>Transactions: <span class="text-cyber-purple font-orbitron font-bold"
                                                                     data-format="number"
                                                                     data-value="{{ block.transactions|length if block.transactions else 0 }}">{{ block.transactions|length if block.transactions else 0 }}</span></div>
                                            <div>Miner: <span class="text-secondary">{{ block.miner or 'Unknown' }}</span></div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm text-muted">{{ block.timestamp|timestamp_to_time if block.timestamp else 'Unknown' }}</div>
                                        <div class="text-xs text-tertiary">{{ block.timestamp|timestamp_to_date if block.timestamp else '' }}</div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-12">
                                <div class="w-20 h-20 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-3xl flex items-center justify-center mx-auto mb-6 animate-pulse">
                                    <svg class="w-10 h-10 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-xl font-orbitron font-bold text-text-primary mb-3">Genesis Awaits</h3>
                                <p class="text-text-secondary mb-4">The blockchain is ready for its first block</p>
                                <p class="text-sm text-text-muted">Start mining or create transactions to see blocks appear here</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="glass-card">
                <div class="card-header">
                    <div class="flex items-center justify-between">
                        <h2 class="card-title">💫 Recent Transactions</h2>
                        <div class="flex items-center gap-2">
                            <button class="refresh-button glass-button-sm px-3 py-1 text-xs min-h-[44px]" data-refresh="transactions" title="Refresh Transactions">
                                🔄
                            </button>
                            <a href="{{ url_for('explorer.transactions') }}" class="text-cyber-purple hover:text-primary transition-normal text-sm">
                                View All →
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="space-y-3" id="recent-transactions-container">
                        {% if recent_transactions %}
                            {% for tx in recent_transactions[:5] %}
                            <div class="glass-card p-4 hover:bg-glass-hover transition-normal">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="flex items-center space-x-3 mb-2">
                                            <span class="text-sm font-orbitron font-bold text-primary">{{ tx.op or 'Transaction' }}</span>
                                            {% if tx.status == 'confirmed' %}
                                            <span class="px-2 py-1 bg-cyber-green/20 text-cyber-green text-xs rounded-md font-medium">CONFIRMED</span>
                                            {% elif tx.status == 'pending' %}
                                            <span class="px-2 py-1 bg-cyber-yellow/20 text-cyber-yellow text-xs rounded-md font-medium">PENDING</span>
                                            {% else %}
                                            <span class="px-2 py-1 bg-cyber-red/20 text-cyber-red text-xs rounded-md font-medium">FAILED</span>
                                            {% endif %}
                                        </div>
                                        <div class="text-sm text-muted space-y-1">
                                            <div>TX: <span class="text-cyber-purple font-mono text-xs"
                                                           data-format="hash"
                                                           data-value="{{ tx.tx_id }}">{{ tx.tx_id[:16] }}...</span></div>
                                            <div>From: <span class="text-secondary">{{ tx.sender or 'System' }}</span></div>
                                            <div>Data: <span class="text-secondary">{{ tx.data[:50] + '...' if tx.data and tx.data|length > 50 else tx.data or 'N/A' }}</span></div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm text-muted">{{ tx.created_at|timestamp_to_time if tx.created_at else 'Unknown' }}</div>
                                        <div class="text-xs text-tertiary">{{ tx.created_at|timestamp_to_date if tx.created_at else '' }}</div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-12">
                                <div class="w-20 h-20 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-3xl flex items-center justify-center mx-auto mb-6 animate-pulse">
                                    <svg class="w-10 h-10 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                    </svg>
                                </div>
                                <h3 class="text-xl font-orbitron font-bold text-text-primary mb-3">Network Ready</h3>
                                <p class="text-text-secondary mb-4">Waiting for the first transactions</p>
                                <p class="text-sm text-text-muted">Register as a validator or citizen to start transacting</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Selas -->
        <div class="glass-card mb-8">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h2 class="card-title">🏢 Recent Sela Businesses</h2>
                    <div class="flex items-center gap-2">
                        <button class="refresh-button glass-button-sm px-3 py-1 text-xs min-h-[44px]" data-refresh="selas" title="Refresh Selas">
                            🔄
                        </button>
                        <a href="{{ url_for('sela.directory') }}" class="text-cyber-cyan hover:text-primary transition-normal text-sm">
                            View All →
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="space-y-3" id="recent-selas-container">
                    <!-- Recent Selas will be loaded here -->
                    <div class="text-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-cyber-cyan mx-auto mb-3"></div>
                        <span class="text-text-secondary">Loading recent Selas...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-Time P2P Network Monitoring -->
        <div class="glass-card mb-8">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="card-title">🌐 P2P Network Status</h2>
                        <p class="card-subtitle">Real-time covenant blockchain network monitoring</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <div id="p2p-status-indicator" class="w-3 h-3 bg-green-500 rounded-full animate-pulse mr-2"></div>
                            <span class="text-sm text-gray-300">Network: <span id="p2p-status-text">Connected</span></span>
                        </div>
                        <label class="auto-refresh-toggle">
                            <input type="checkbox" id="auto-refresh-toggle" checked>
                            <span class="text-xs">Auto Refresh</span>
                        </label>
                        <button onclick="refreshNetworkData()" class="glass-button-sm px-3 py-1 text-xs min-h-[44px]">
                            <i class="fas fa-sync-alt mr-1"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <!-- Bootstrap Node Status -->
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-orbitron font-bold text-cyber-cyan mb-2" id="bootstrap-status">Active</div>
                        <div class="text-sm text-tertiary">Bootstrap Node</div>
                    </div>

                    <!-- Tribal Elders -->
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-orbitron font-bold text-cyber-purple mb-2" id="tribal-elders-count">0</div>
                        <div class="text-sm text-tertiary">Tribal Elders</div>
                    </div>

                    <!-- Mining Nodes -->
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-orbitron font-bold text-cyber-blue mb-2" id="mining-nodes-count">0</div>
                        <div class="text-sm text-tertiary">Mining Nodes</div>
                    </div>

                    <!-- Network Health -->
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-orbitron font-bold text-cyber-green mb-2" id="network-health">Unknown</div>
                        <div class="text-sm text-tertiary">Network Health</div>
                    </div>
                </div>

                <!-- Tribal Elder Council Status -->
                <div class="mb-6">
                    <h3 class="text-lg font-orbitron font-bold text-primary mb-4">👑 Tribal Elder Council</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3" id="tribal-elders-grid">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Biblical Compliance Metrics -->
                <div class="mb-6">
                    <h3 class="text-lg font-orbitron font-bold text-primary mb-4">📜 Biblical Compliance</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="glass-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-muted">Anti-Usury</span>
                                <span class="text-xs text-gray-500" id="anti-usury-score">N/A</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-gray-500 h-2 rounded-full" style="width: 0%" id="anti-usury-bar"></div>
                            </div>
                        </div>

                        <div class="glass-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-muted">Sabbath Observance</span>
                                <span class="text-xs text-gray-500" id="sabbath-score">N/A</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-gray-500 h-2 rounded-full" style="width: 0%" id="sabbath-bar"></div>
                            </div>
                        </div>

                        <div class="glass-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-muted">Gleaning Pools</span>
                                <span class="text-xs text-gray-500" id="gleaning-score">N/A</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-gray-500 h-2 rounded-full" style="width: 0%" id="gleaning-bar"></div>
                            </div>
                        </div>

                        <div class="glass-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-muted">Tribal Governance</span>
                                <span class="text-xs text-gray-500" id="governance-score">N/A</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-gray-500 h-2 rounded-full" style="width: 0%" id="governance-bar"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mining Activity -->
                <div>
                    <h3 class="text-lg font-orbitron font-bold text-primary mb-4">⛏️ Mining Activity</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-1">Active Proposals</div>
                            <div class="text-xl font-orbitron font-bold text-cyber-cyan" id="active-proposals">0</div>
                        </div>

                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-1">Proposals/Hour</div>
                            <div class="text-xl font-orbitron font-bold text-cyber-purple" id="proposals-per-hour">0</div>
                        </div>

                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-1">Consensus Health</div>
                            <div class="text-xl font-orbitron font-bold text-cyber-green" id="consensus-health">N/A</div>
                        </div>
                    </div>

                    <!-- Live Mining Status -->
                    <div class="glass-card p-4 mb-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-orbitron font-bold text-cyber-cyan">🔥 Live Mining Status</h4>
                            <button onclick="refreshMiningStatus()" class="glass-button-sm px-3 py-1 text-xs">
                                <i class="fas fa-sync-alt mr-1"></i> Refresh
                            </button>
                        </div>
                        <div id="live-mining-status">
                            <div class="flex items-center justify-center py-4">
                                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-cyan mr-3"></div>
                                <span class="text-muted">Loading mining status...</span>
                            </div>
                        </div>
                    </div>

                    <!-- GPU Usage Alert - Only show when there's actual mining activity -->
                    <div id="gpu-usage-alert" class="glass-card p-4 border-cyber-yellow bg-cyber-yellow/5" style="display: none;">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-cyber-yellow mr-3"></i>
                            <div>
                                <div class="font-orbitron font-bold text-cyber-yellow">Mining Activity Detected</div>
                                <div class="text-sm text-muted">Check the mining status above to see active miners</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Mining Dashboard -->
        <div class="glass-card mb-8">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="card-title">⛏️ Live Mining Dashboard</h2>
                        <p class="card-subtitle">Real-time mining statistics and biblical compliance</p>
                    </div>
                    <button class="refresh-button glass-button-sm px-3 py-1 text-xs min-h-[44px]" data-refresh="mining" title="Refresh Mining Stats">
                        🔄
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="mining-stats-container" class="loading">
                    <!-- Mining stats grid -->
                    <div class="mining-stats-grid">
                        <div class="stat-card">
                            <span class="stat-label">Current Difficulty</span>
                            <span class="stat-value" id="current-difficulty">—</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-label">Network Hashrate</span>
                            <span class="stat-value" id="network-hashrate">—</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-label">Active Miners</span>
                            <span class="stat-value" id="active-miners">—</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-label">Blocks Today</span>
                            <span class="stat-value" id="blocks-today">—</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-label">Avg Block Time</span>
                            <span class="stat-value" id="avg-block-time">—</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-label">Rewards Today</span>
                            <span class="stat-value" id="mining-rewards-today">—</span>
                        </div>
                    </div>

                    <!-- Sabbath compliance -->
                    <div class="mt-6">
                        <h4 class="text-lg font-orbitron font-bold text-primary mb-4">🕯️ Sabbath Compliance</h4>
                        <div id="sabbath-status" class="flex items-center gap-4">
                            <span class="sabbath-indicator sabbath-inactive">⚡ Active</span>
                            <span class="compliance-status compliant">Compliant</span>
                        </div>
                    </div>

                    <!-- Tribal mining distribution -->
                    <div class="mt-6">
                        <h4 class="text-lg font-orbitron font-bold text-primary mb-4">👑 Tribal Mining Distribution</h4>
                        <div id="tribal-mining-chart">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Network Health Dashboard -->
        <div class="glass-card mb-8">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="card-title">🌐 Network Health Dashboard</h2>
                        <p class="card-subtitle">Real-time network monitoring and covenant compliance</p>
                    </div>
                    <button class="refresh-button glass-button-sm px-3 py-1 text-xs min-h-[44px]" data-refresh="network" title="Refresh Network Stats">
                        🔄
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="network-stats-container" class="loading">
                    <!-- Network metrics grid -->
                    <div class="mining-stats-grid">
                        <div class="stat-card">
                            <span class="stat-label">Active Nodes</span>
                            <span class="stat-value" id="node-count">—</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-label">Network Latency</span>
                            <span class="stat-value" id="network-latency">—</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-label">Covenant Compliance</span>
                            <span class="stat-value" id="covenant-compliance">—</span>
                        </div>
                    </div>

                    <!-- Consensus status -->
                    <div class="mt-6">
                        <h4 class="text-lg font-orbitron font-bold text-primary mb-4">⚖️ Consensus Status</h4>
                        <div id="consensus-status">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Sync status -->
                    <div class="mt-6">
                        <h4 class="text-lg font-orbitron font-bold text-primary mb-4">🔄 Sync Status</h4>
                        <div id="sync-status">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Tribal node distribution -->
                    <div class="mt-6">
                        <h4 class="text-lg font-orbitron font-bold text-primary mb-4">🏛️ Tribal Node Distribution</h4>
                        <div id="tribal-nodes-chart">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>

                    <!-- Governance activity -->
                    <div class="mt-6">
                        <h4 class="text-lg font-orbitron font-bold text-primary mb-4">👑 Governance Activity</h4>
                        <div id="governance-activity">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Precision Network Analytics -->
        <div class="glass-card mb-8">
            <div class="card-header">
                <h2 class="card-title">📊 Network Analytics</h2>
                <p class="card-subtitle">Real-time blockchain performance metrics</p>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Transaction Volume Chart Placeholder -->
                    <div class="col-span-2">
                        <div class="h-64 bg-gradient-to-br from-cyber-cyan/10 to-cyber-purple/10 rounded-2xl flex items-center justify-center">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-2xl flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                                <p class="text-muted font-orbitron">Transaction Volume Chart</p>
                                <p class="text-sm text-tertiary">Coming Soon</p>
                            </div>
                        </div>
                    </div>

                    <!-- Network Stats with Precision -->
                    <div class="space-y-3">
                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-1">Average Block Time</div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-cyan">{{ avg_block_time or '~10s' }}</div>
                        </div>
                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-1">Network Difficulty</div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-purple">{{ network_difficulty or 'Auto' }}</div>
                        </div>
                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-1">Active Validators</div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-blue"
                                 data-format="number"
                                 data-value="{{ active_validators or 0 }}">{{ active_validators or 0 }}</div>
                        </div>
                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-1">Total Supply</div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-green"
                                 data-format="currency"
                                 data-value="{{ total_supply or 0 }}">{{ total_supply or 'N/A' }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Why Transparency Matters Section -->
        <div class="mt-16 mb-16">
            <div class="glass-card-enhanced p-12">
                <h2 class="text-4xl font-orbitron font-bold text-center mb-8">
                    <span class="hologram-text">Why Transparency Matters</span>
                </h2>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
                    <!-- Business Benefits -->
                    <div>
                        <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">For Your Business</h3>

                        <div class="space-y-6">
                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-xl flex items-center justify-center flex-shrink-0">
                                    <span class="text-xl">🛡️</span>
                                </div>
                                <div>
                                    <h4 class="font-bold text-text-primary mb-2">Build Customer Trust</h4>
                                    <p class="text-text-secondary">Every transaction is permanently recorded and verifiable. Customers can see your business history, building unshakeable trust.</p>
                                </div>
                            </div>

                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-xl flex items-center justify-center flex-shrink-0">
                                    <span class="text-xl">📈</span>
                                </div>
                                <div>
                                    <h4 class="font-bold text-text-primary mb-2">Prove Your Reputation</h4>
                                    <p class="text-text-secondary">Blockchain verification provides undeniable proof of your business practices, opening doors to enterprise contracts.</p>
                                </div>
                            </div>

                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-cyber-blue to-cyber-green rounded-xl flex items-center justify-center flex-shrink-0">
                                    <span class="text-xl">⚡</span>
                                </div>
                                <div>
                                    <h4 class="font-bold text-text-primary mb-2">Instant Verification</h4>
                                    <p class="text-text-secondary">No more waiting for third-party verification. Your blockchain record speaks for itself, instantly and globally.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Technical Simplified -->
                    <div>
                        <h3 class="text-2xl font-orbitron font-bold text-cyber-purple mb-6">How It Works (Simply)</h3>

                        <div class="space-y-6">
                            <div class="glass-card p-6 rounded-xl">
                                <h4 class="font-bold text-cyber-cyan mb-3">🔗 What is a "Block"?</h4>
                                <p class="text-text-secondary">Think of a block as a digital filing cabinet that stores business transactions. Once filed, it can never be changed or deleted.</p>
                            </div>

                            <div class="glass-card p-6 rounded-xl">
                                <h4 class="font-bold text-cyber-purple mb-3">💫 What is a "Transaction"?</h4>
                                <p class="text-text-secondary">Any business action: registering your company, completing a sale, earning rewards. Each gets a permanent, tamper-proof record.</p>
                            </div>

                            <div class="glass-card p-6 rounded-xl">
                                <h4 class="font-bold text-cyber-blue mb-3">🏢 What is a "Validator"?</h4>
                                <p class="text-text-secondary">Verified businesses that help secure the network. They earn rewards while ensuring all transactions are legitimate.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Success Stories -->
                <div class="glass-card p-8 rounded-2xl mb-8">
                    <h3 class="text-2xl font-orbitron font-bold text-cyber-green mb-6 text-center">Real Business Impact</h3>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">40%</div>
                            <div class="text-sm text-text-secondary">Increase in customer trust scores</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-orbitron font-bold text-cyber-purple mb-2">85%</div>
                            <div class="text-sm text-text-secondary">Faster contract approvals</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-orbitron font-bold text-cyber-blue mb-2">60%</div>
                            <div class="text-sm text-text-secondary">Reduction in verification time</div>
                        </div>
                    </div>
                </div>

                <!-- Call to Action for Non-Users -->
                {% if not current_user %}
                <div class="text-center">
                    <h3 class="text-xl font-orbitron font-bold text-text-primary mb-6">Ready to Build Unshakeable Trust?</h3>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ url_for('register_choice') }}"
                           class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-bold">
                            ✨ Verify Your Business
                        </a>
                        <a href="{{ url_for('sela.directory') }}"
                           class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold">
                            🏢 See Verified Businesses
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Blockchain Glossary for Business Users -->
        <div class="mt-16 mb-16">
            <div class="glass-card p-8">
                <h2 class="text-3xl font-orbitron font-bold text-center mb-8">
                    <span class="hologram-text">Blockchain Terms Made Simple</span>
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="glass-card p-6 rounded-lg">
                        <h4 class="font-bold text-cyber-cyan mb-3">Hash</h4>
                        <p class="text-sm text-text-secondary">A unique digital fingerprint for each transaction. Like a receipt number that can never be duplicated.</p>
                    </div>

                    <div class="glass-card p-6 rounded-lg">
                        <h4 class="font-bold text-cyber-purple mb-3">Mining</h4>
                        <p class="text-sm text-text-secondary">The process of verifying and recording transactions. Miners earn rewards for keeping the network secure.</p>
                    </div>

                    <div class="glass-card p-6 rounded-lg">
                        <h4 class="font-bold text-cyber-blue mb-3">Network</h4>
                        <p class="text-sm text-text-secondary">All the computers working together to maintain the blockchain. More participants = more security.</p>
                    </div>

                    <div class="glass-card p-6 rounded-lg">
                        <h4 class="font-bold text-cyber-green mb-3">Confirmation</h4>
                        <p class="text-sm text-text-secondary">When a transaction is verified and permanently added to the blockchain. Usually takes seconds.</p>
                    </div>

                    <div class="glass-card p-6 rounded-lg">
                        <h4 class="font-bold text-cyber-cyan mb-3">Wallet Address</h4>
                        <p class="text-sm text-text-secondary">Your unique business identifier on the blockchain. Like a bank account number, but public and verifiable.</p>
                    </div>

                    <div class="glass-card p-6 rounded-lg">
                        <h4 class="font-bold text-cyber-purple mb-3">Smart Contract</h4>
                        <p class="text-sm text-text-secondary">Automated business agreements that execute themselves when conditions are met. No middleman needed.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Precision Quick Actions -->
        <div class="text-center">
            <h2 class="text-2xl font-orbitron font-bold text-text-primary mb-8">Explore the Network</h2>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ url_for('explorer.blocks') }}"
                   class="glass-button px-8 py-4 rounded-lg font-orbitron font-bold transition-normal hover:scale-105">
                    🔗 Browse Blocks
                </a>
                <a href="{{ url_for('explorer.transactions') }}"
                   class="glass-button px-8 py-4 rounded-lg font-orbitron font-bold transition-normal hover:scale-105">
                    💫 View Transactions
                </a>
                <a href="{{ url_for('sela.directory') }}"
                   class="glass-button px-8 py-4 rounded-lg font-orbitron font-bold transition-normal hover:scale-105">
                    🏢 Validator Network
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Real-time P2P Network Monitoring
let networkUpdateInterval;

// Initialize real-time monitoring
document.addEventListener('DOMContentLoaded', function() {
    loadNetworkData();
    loadMiningStatus();
    loadRecentSelas();
    startNetworkMonitoring();
});

// Load P2P network data
async function loadNetworkData() {
    try {
        const response = await fetch('/explorer/api/live-data');
        const data = await response.json();

        if (data.success) {
            updateNetworkStatus(data);
            updateTribalElders(data.tribal_elders);
            updateBiblicalCompliance(data.biblical_compliance);
            updateMiningActivity(data.mining);
        }
    } catch (error) {
        console.error('Failed to load network data:', error);
        showNetworkError();
    }
}

// Update network status indicators
function updateNetworkStatus(data) {
    const network = data.network;

    // Update status indicators
    const statusIndicator = document.getElementById('p2p-status-indicator');
    const statusText = document.getElementById('p2p-status-text');

    if (network && network.bootstrap_node && network.bootstrap_node.status === 'active') {
        statusIndicator.className = 'w-3 h-3 bg-green-500 rounded-full animate-pulse mr-2';
        statusText.textContent = 'Connected';
    } else {
        statusIndicator.className = 'w-3 h-3 bg-red-500 rounded-full mr-2';
        statusText.textContent = 'Disconnected';
    }

    // Update network metrics
    document.getElementById('bootstrap-status').textContent =
        network.bootstrap_node ? 'Active' : 'Offline';
    document.getElementById('tribal-elders-count').textContent =
        Object.keys(network.tribal_elders || {}).length || '0';
    document.getElementById('mining-nodes-count').textContent =
        network.mining_nodes || '0';
    document.getElementById('network-health').textContent =
        network.network_health || 'Unknown';

    // Update last updated time
    const lastUpdated = new Date().toLocaleTimeString();
    const lastUpdatedElement = document.getElementById('last-updated');
    if (lastUpdatedElement) {
        lastUpdatedElement.textContent = lastUpdated;
    }
}

// Update tribal elders grid
function updateTribalElders(elders) {
    const grid = document.getElementById('tribal-elders-grid');
    if (!grid) return;

    // If no elders data or empty, show empty state
    if (!elders || Object.keys(elders).length === 0) {
        grid.innerHTML = `
            <div class="col-span-full text-center py-8">
                <div class="text-muted">No tribal elders registered</div>
                <div class="text-sm text-tertiary mt-2">Elders will appear here once registered</div>
            </div>
        `;
        return;
    }

    // Show actual elders from database
    grid.innerHTML = Object.entries(elders).map(([code, elder]) => {
        const isActive = elder.status === 'active';
        const votingWeight = elder.voting_weight || 1;
        const isMajorTribe = ['JU', 'LE', 'EP'].includes(code);

        return `
            <div class="glass-card p-3 text-center ${isActive ? 'border-cyber-green' : 'border-gray-600'}">
                <div class="flex items-center justify-center mb-2">
                    <div class="w-2 h-2 ${isActive ? 'bg-cyber-green' : 'bg-gray-500'} rounded-full mr-2"></div>
                    <span class="font-orbitron font-bold text-sm ${isMajorTribe ? 'text-cyber-cyan' : 'text-primary'}">${code}</span>
                </div>
                <div class="text-xs text-muted">
                    ${votingWeight}x vote${votingWeight > 1 ? 's' : ''}
                </div>
                <div class="text-xs text-tertiary">
                    ${elder.connected_peers || 0} peers
                </div>
            </div>
        `;
    }).join('');
}

// Update biblical compliance metrics
function updateBiblicalCompliance(compliance) {
    // If no compliance data, show N/A state
    if (!compliance) {
        const metrics = ['anti-usury', 'sabbath', 'gleaning', 'governance'];
        metrics.forEach(metric => {
            const scoreElement = document.getElementById(`${metric}-score`);
            const barElement = document.getElementById(`${metric}-bar`);

            if (scoreElement) {
                scoreElement.textContent = 'N/A';
                scoreElement.className = 'text-xs text-gray-500';
            }
            if (barElement) {
                barElement.style.width = '0%';
                barElement.className = 'bg-gray-500 h-2 rounded-full';
            }
        });
        return;
    }

    const metrics = [
        { id: 'anti-usury', score: compliance.anti_usury_score || 0, color: 'cyber-green' },
        { id: 'sabbath', score: compliance.sabbath_observance_score || 0, color: 'cyber-green' },
        { id: 'gleaning', score: compliance.gleaning_participation_score || 0, color: 'cyber-cyan' },
        { id: 'governance', score: compliance.tribal_governance_score || 0, color: 'cyber-purple' }
    ];

    metrics.forEach(metric => {
        const percentage = Math.round(metric.score * 100);
        const scoreElement = document.getElementById(`${metric.id}-score`);
        const barElement = document.getElementById(`${metric.id}-bar`);

        if (scoreElement) {
            scoreElement.textContent = `${percentage}%`;
            scoreElement.className = `text-xs text-${metric.color}`;
        }
        if (barElement) {
            barElement.style.width = `${percentage}%`;
            barElement.className = `bg-${metric.color} h-2 rounded-full`;
        }
    });
}

// Update mining activity
function updateMiningActivity(mining) {
    if (!mining) {
        // Show empty state for mining activity
        document.getElementById('active-proposals').textContent = '0';
        document.getElementById('proposals-per-hour').textContent = '0';
        document.getElementById('consensus-health').textContent = 'N/A';
        return;
    }

    document.getElementById('active-proposals').textContent = mining.proposals_submitted || 0;
    document.getElementById('proposals-per-hour').textContent = mining.proposals_per_hour || 0;

    const consensusHealth = mining.biblical_compliance_rate ?
        Math.round(mining.biblical_compliance_rate * 100) + '%' : 'N/A';
    document.getElementById('consensus-health').textContent = consensusHealth;
}

// Show network error state
function showNetworkError() {
    const statusIndicator = document.getElementById('p2p-status-indicator');
    const statusText = document.getElementById('p2p-status-text');

    statusIndicator.className = 'w-3 h-3 bg-yellow-500 rounded-full mr-2';
    statusText.textContent = 'Connecting...';
}

// Start real-time monitoring
function startNetworkMonitoring() {
    // Update every 10 seconds
    networkUpdateInterval = setInterval(loadNetworkData, 10000);
}

// Stop monitoring
function stopNetworkMonitoring() {
    if (networkUpdateInterval) {
        clearInterval(networkUpdateInterval);
    }
}

// Load recent Selas
async function loadRecentSelas() {
    try {
        const response = await fetch('/sela/api/recent');
        const data = await response.json();

        if (data && data.length > 0) {
            updateRecentSelas(data);
        } else {
            showEmptySelas();
        }
    } catch (error) {
        console.error('Failed to load recent Selas:', error);
        showSelasError();
    }
}

// Update recent Selas display
function updateRecentSelas(selas) {
    const container = document.getElementById('recent-selas-container');
    if (!container) return;

    const selasHtml = selas.slice(0, 5).map(sela => {
        const metadata = sela.metadata_parsed || {};
        const description = metadata.description || 'No description available';
        const createdDate = new Date(sela.created_at * 1000);

        return `
            <div class="glass-card p-4 hover:bg-glass-hover transition-normal">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="flex items-center space-x-3 mb-2">
                            <span class="text-lg font-orbitron font-bold text-primary">
                                <a href="/sela/${sela.sela_id}" class="hover:text-cyber-cyan transition-colors">
                                    ${sela.name}
                                </a>
                            </span>
                            <span class="px-2 py-1 bg-cyber-green/20 text-cyber-green text-xs rounded-md font-medium">ACTIVE</span>
                        </div>
                        <div class="text-sm text-muted space-y-1">
                            <div>Category: <span class="text-cyber-purple">${sela.category}</span></div>
                            <div>Stake: <span class="text-cyber-cyan">${sela.stake_amount} ${sela.stake_token_id}</span></div>
                            <div>Description: <span class="text-secondary">${description.substring(0, 50)}${description.length > 50 ? '...' : ''}</span></div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-muted">${createdDate.toLocaleTimeString()}</div>
                        <div class="text-xs text-tertiary">${createdDate.toLocaleDateString()}</div>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = selasHtml;
}

// Show empty Selas state
function showEmptySelas() {
    const container = document.getElementById('recent-selas-container');
    if (!container) return;

    container.innerHTML = `
        <div class="text-center py-12">
            <div class="w-20 h-20 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-3xl flex items-center justify-center mx-auto mb-6 animate-pulse">
                <span class="text-3xl">🏢</span>
            </div>
            <h3 class="text-xl font-orbitron font-bold text-text-primary mb-3">No Sela Businesses Yet</h3>
            <p class="text-text-secondary mb-4">Waiting for the first business validators</p>
            <p class="text-sm text-text-muted">Register your business to become a validator</p>
        </div>
    `;
}

// Show Selas error state
function showSelasError() {
    const container = document.getElementById('recent-selas-container');
    if (!container) return;

    container.innerHTML = `
        <div class="text-center py-8">
            <i class="fas fa-exclamation-triangle text-cyber-yellow text-2xl mb-2"></i>
            <div class="text-muted">Unable to load Sela businesses</div>
            <button onclick="loadRecentSelas()" class="glass-button-sm px-3 py-1 mt-2 text-xs">
                Try Again
            </button>
        </div>
    `;
}

// Load mining status
async function loadMiningStatus() {
    try {
        // First try to get data from database directly
        const response = await fetch('/explorer/api/live-data');
        const data = await response.json();

        if (data.success) {
            updateMiningStatus(data);
        } else {
            showMiningError();
        }
    } catch (error) {
        console.error('Failed to load mining status:', error);
        showMiningError();
    }
}

// Update mining status display
function updateMiningStatus(data) {
    const statusContainer = document.getElementById('live-mining-status');

    // Check if we have any mining data
    if (!data || !data.mining || data.mining.active_miners === 0) {
        statusContainer.innerHTML = `
            <div class="text-center py-8">
                <div class="text-muted">No active miners</div>
                <div class="text-sm text-tertiary mt-2">Mining activity will appear here once started</div>
            </div>
        `;
        return;
    }

    // Show actual mining data if available
    const mining = data.mining;
    let html = '<div class="space-y-3">';

    // Create miners based on actual data
    if (mining.active_miners > 0) {
        for (let i = 0; i < mining.active_miners; i++) {
            html += `
                <div class="flex items-center justify-between p-3 bg-glass-bg rounded-lg border border-glass-border">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-cyber-green rounded-full mr-3 animate-pulse"></div>
                        <div>
                            <div class="font-orbitron font-bold text-primary">🔥 ACTIVE_MINER_${i + 1}</div>
                            <div class="text-sm text-muted">Status: ACTIVE</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-lg font-orbitron font-bold text-cyber-cyan">${mining.proposals_submitted || 0}</div>
                        <div class="text-xs text-muted">proposals submitted</div>
                    </div>
                </div>
            `;
        }
    }

    html += '</div>';
    statusContainer.innerHTML = html;
}

// Show mining error state
function showMiningError() {
    const statusContainer = document.getElementById('live-mining-status');
    statusContainer.innerHTML = `
        <div class="text-center py-4">
            <i class="fas fa-exclamation-triangle text-cyber-yellow text-2xl mb-2"></i>
            <div class="text-muted">Unable to load mining status</div>
            <button onclick="loadMiningStatus()" class="glass-button-sm px-3 py-1 mt-2 text-xs">
                Try Again
            </button>
        </div>
    `;
}

// Manual refresh function
async function refreshNetworkData() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Refreshing...';
    button.disabled = true;

    await loadNetworkData();

    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 1000);
}

// Manual mining status refresh
async function refreshMiningStatus() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Refreshing...';
    button.disabled = true;

    await loadMiningStatus();

    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 1000);
}

// Search functionality
function performSearch() {
    const searchTerm = document.getElementById('blockchain-search').value.trim();
    if (!searchTerm) {
        if (typeof Onnyx !== 'undefined' && Onnyx.utils) {
            Onnyx.utils.showNotification('Please enter a search term', 'warning');
        }
        return;
    }

    // Show loading state
    const searchButton = event.target;
    const originalText = searchButton.textContent;
    searchButton.textContent = 'Searching...';
    searchButton.disabled = true;

    // Simulate search (replace with actual API call)
    setTimeout(() => {
        searchButton.textContent = originalText;
        searchButton.disabled = false;

        // For demo purposes, show a message
        if (searchTerm.length === 64) {
            if (typeof Onnyx !== 'undefined' && Onnyx.utils) {
                Onnyx.utils.showNotification('Block/Transaction hash detected', 'info');
            }
        } else if (searchTerm.length === 42) {
            if (typeof Onnyx !== 'undefined' && Onnyx.utils) {
                Onnyx.utils.showNotification('Address detected', 'info');
            }
        } else {
            if (typeof Onnyx !== 'undefined' && Onnyx.utils) {
                Onnyx.utils.showNotification('Search functionality coming soon', 'info');
            }
        }
    }, 1000);
}

// Manual refresh function for Selas
async function refreshSelas() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Refreshing...';
    button.disabled = true;

    await loadRecentSelas();

    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 1000);
}

// Add event listener for Sela refresh button
document.addEventListener('DOMContentLoaded', function() {
    const selaRefreshButton = document.querySelector('[data-refresh="selas"]');
    if (selaRefreshButton) {
        selaRefreshButton.addEventListener('click', refreshSelas);
    }
});

// Auto-refresh blockchain data every 30 seconds
setInterval(async () => {
    try {
        // Refresh network stats
        const response = await fetch('/explorer/api/network-stats');
        const data = await response.json();

        if (data.success && data.blockchain) {
            const stats = data.blockchain;
            const latestBlockElement = document.getElementById('latest-block');
            const totalTxElement = document.getElementById('total-transactions');

            if (latestBlockElement) latestBlockElement.textContent = stats.latest_block || 0;
            if (totalTxElement) totalTxElement.textContent = stats.total_transactions || 0;
        }

        // Also refresh Selas periodically
        loadRecentSelas();
    } catch (error) {
        console.log('Auto-refresh failed:', error);
    }
}, 30000);

// Enter key search
document.getElementById('blockchain-search')?.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        performSearch();
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    stopNetworkMonitoring();
});
</script>

<script>
// Enhanced Explorer Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeExplorer();
    setupSearchFunctionality();
    setupAutoRefresh();
    setupLoadingStates();
});

function initializeExplorer() {
    // Initialize number formatting
    formatNumbers();

    // Setup hover effects
    setupHoverEffects();

    // Initialize tooltips
    setupTooltips();
}

function setupSearchFunctionality() {
    const searchInput = document.getElementById('blockchain-search');
    const searchResults = document.getElementById('search-results');
    const searchResultsContent = document.getElementById('search-results-content');

    // Real-time search suggestions
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();
        if (query.length > 3) {
            showSearchSuggestions(query);
        } else {
            hideSearchResults();
        }
    });

    // Enter key search
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
}

function performSearch() {
    const searchInput = document.getElementById('blockchain-search');
    const searchResults = document.getElementById('search-results');
    const searchResultsContent = document.getElementById('search-results-content');
    const query = searchInput.value.trim();

    if (!query) {
        showSearchError('Please enter a search term');
        return;
    }

    // Show loading state
    searchResults.classList.remove('hidden');
    searchResultsContent.innerHTML = `
        <div class="flex items-center justify-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-cyber-cyan mr-3"></div>
            <span class="text-text-secondary">Searching blockchain...</span>
        </div>
    `;

    // Simulate search (replace with actual API call)
    setTimeout(() => {
        if (query.length === 64) {
            // Looks like a hash
            showHashResults(query);
        } else if (query.includes('@') || query.includes('.')) {
            // Looks like an address
            showAddressResults(query);
        } else {
            // General search
            showGeneralResults(query);
        }
    }, 1000);
}

function showHashResults(hash) {
    const searchResultsContent = document.getElementById('search-results-content');
    searchResultsContent.innerHTML = `
        <div class="space-y-4">
            <div class="glass-card p-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-orbitron font-bold text-cyber-cyan">Block Found</h4>
                    <span class="px-2 py-1 bg-cyber-green/20 text-cyber-green text-xs rounded-md">CONFIRMED</span>
                </div>
                <div class="space-y-2 text-sm">
                    <div><span class="text-text-muted">Hash:</span> <span class="text-cyber-cyan font-mono">${hash.substring(0, 16)}...</span></div>
                    <div><span class="text-text-muted">Height:</span> <span class="text-text-primary">1,234</span></div>
                    <div><span class="text-text-muted">Transactions:</span> <span class="text-cyber-purple">5</span></div>
                    <div><span class="text-text-muted">Timestamp:</span> <span class="text-text-secondary">2024-01-15 14:30:25</span></div>
                </div>
            </div>
        </div>
    `;
}

function showAddressResults(address) {
    const searchResultsContent = document.getElementById('search-results-content');
    searchResultsContent.innerHTML = `
        <div class="space-y-4">
            <div class="glass-card p-4">
                <h4 class="font-orbitron font-bold text-cyber-purple mb-3">Address Details</h4>
                <div class="space-y-2 text-sm">
                    <div><span class="text-text-muted">Address:</span> <span class="text-cyber-purple font-mono">${address}</span></div>
                    <div><span class="text-text-muted">Balance:</span> <span class="text-cyber-green">1,250.50 ONX</span></div>
                    <div><span class="text-text-muted">Transactions:</span> <span class="text-text-primary">23</span></div>
                    <div><span class="text-text-muted">First Seen:</span> <span class="text-text-secondary">2024-01-10 09:15:42</span></div>
                </div>
            </div>
        </div>
    `;
}

function showGeneralResults(query) {
    const searchResultsContent = document.getElementById('search-results-content');
    searchResultsContent.innerHTML = `
        <div class="text-center py-8">
            <div class="w-16 h-16 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-2xl flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 6.306a7.962 7.962 0 00-6 0M3 3l18 18"></path>
                </svg>
            </div>
            <h3 class="text-lg font-orbitron font-bold text-text-primary mb-2">No Results Found</h3>
            <p class="text-text-secondary mb-4">No matches found for "${query}"</p>
            <p class="text-sm text-text-muted">Try searching with a block hash, transaction ID, or address</p>
        </div>
    `;
}

function showSearchError(message) {
    const searchResults = document.getElementById('search-results');
    const searchResultsContent = document.getElementById('search-results-content');

    searchResults.classList.remove('hidden');
    searchResultsContent.innerHTML = `
        <div class="text-center py-6">
            <div class="w-12 h-12 bg-cyber-red/20 rounded-xl flex items-center justify-center mx-auto mb-3">
                <svg class="w-6 h-6 text-cyber-red" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="text-cyber-red font-medium">${message}</p>
        </div>
    `;
}

function hideSearchResults() {
    const searchResults = document.getElementById('search-results');
    searchResults.classList.add('hidden');
}

function setupAutoRefresh() {
    // Auto-refresh network data every 30 seconds
    setInterval(() => {
        refreshNetworkData();
    }, 30000);
}

function refreshNetworkData() {
    // Simulate network data refresh
    console.log('Refreshing network data...');

    // Update network status indicators
    updateNetworkStatus();
}

function updateNetworkStatus() {
    const statusIndicator = document.getElementById('p2p-status-indicator');
    const statusText = document.getElementById('p2p-status-text');

    if (statusIndicator && statusText) {
        statusIndicator.className = 'w-3 h-3 bg-cyber-green rounded-full animate-pulse mr-2';
        statusText.textContent = 'Connected';
    }
}

function setupLoadingStates() {
    // Add loading states to interactive elements
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (this.dataset.loading !== 'true') {
                showButtonLoading(this);
            }
        });
    });
}

function showButtonLoading(button) {
    const originalText = button.innerHTML;
    button.dataset.originalText = originalText;
    button.dataset.loading = 'true';
    button.innerHTML = '<span class="animate-spin mr-2">⏳</span>Loading...';
    button.disabled = true;

    // Reset after 2 seconds (adjust as needed)
    setTimeout(() => {
        button.innerHTML = originalText;
        button.dataset.loading = 'false';
        button.disabled = false;
    }, 2000);
}

function formatNumbers() {
    // Format all numbers with data-format attributes
    const numberElements = document.querySelectorAll('[data-format="number"]');
    numberElements.forEach(element => {
        const value = parseInt(element.dataset.value || element.textContent);
        if (!isNaN(value)) {
            element.textContent = value.toLocaleString();
        }
    });

    // Format compact numbers
    const compactElements = document.querySelectorAll('[data-compact="true"]');
    compactElements.forEach(element => {
        const value = parseInt(element.dataset.value || element.textContent);
        if (!isNaN(value)) {
            element.textContent = formatCompactNumber(value);
        }
    });
}

function formatCompactNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

function setupHoverEffects() {
    // Use CSS-only hover effects for consistent behavior across all pages
    // Remove JavaScript overrides to let CSS handle the smooth transitions
    console.log('Using standardized CSS hover effects for consistent user experience');
}

function setupTooltips() {
    // Add tooltips to hash elements
    const hashElements = document.querySelectorAll('[data-format="hash"]');
    hashElements.forEach(element => {
        element.title = element.dataset.value || element.textContent;
        element.style.cursor = 'pointer';

        element.addEventListener('click', function() {
            navigator.clipboard.writeText(this.dataset.value || this.textContent);
            showTooltip(this, 'Copied!');
        });
    });
}

function showTooltip(element, message) {
    const tooltip = document.createElement('div');
    tooltip.className = 'absolute bg-onyx-black text-cyber-cyan text-xs px-2 py-1 rounded shadow-lg z-50';
    tooltip.textContent = message;
    tooltip.style.top = '-30px';
    tooltip.style.left = '50%';
    tooltip.style.transform = 'translateX(-50%)';

    element.style.position = 'relative';
    element.appendChild(tooltip);

    setTimeout(() => {
        tooltip.remove();
    }, 2000);
}

// Refresh mining status function
function refreshMiningStatus() {
    const statusContainer = document.getElementById('live-mining-status');
    if (statusContainer) {
        statusContainer.innerHTML = `
            <div class="flex items-center justify-center py-4">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-cyan mr-3"></div>
                <span class="text-text-muted">Refreshing mining status...</span>
            </div>
        `;

        setTimeout(() => {
            statusContainer.innerHTML = `
                <div class="text-center py-4">
                    <p class="text-text-secondary">No active mining detected</p>
                    <p class="text-sm text-text-muted mt-2">Start mining to see real-time status here</p>
                </div>
            `;
        }, 1500);
    }
}
</script>
</div>

{% endblock %}

{% block extra_js %}
<!-- Live Explorer JavaScript -->
<script src="{{ url_for('static', filename='js/live-explorer.js') }}"></script>
<script src="{{ url_for('static', filename='js/blockchain-data-formatter.js') }}"></script>
{% endblock %}