{% extends "base.html" %}

{% block title %}Community Governance - ONNYX Platform{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black">
    <!-- Hero Section -->
    <div class="py-20 relative overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-16">
                <h1 class="text-5xl md:text-7xl font-orbitron font-bold mb-6">
                    <span class="bg-gradient-to-r from-cyber-cyan via-cyber-purple to-cyber-blue bg-clip-text text-transparent">
                        Community Governance
                    </span>
                </h1>
                <p class="text-xl text-text-secondary max-w-4xl mx-auto leading-relaxed">
                    Transparent biblical governance through the Council of 12 Gate Keepers. 
                    All identity verifications and governance decisions are publicly auditable.
                </p>
            </div>
        </div>
    </div>

    <!-- Governance Statistics -->
    <div class="py-16 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
                <div class="glass-card-premium text-center p-8">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-cyan to-cyber-cyan-bright rounded-xl flex items-center justify-center">
                        <span class="text-2xl">⚖️</span>
                    </div>
                    <div class="metric-value text-cyber-cyan mb-3 font-orbitron font-bold text-4xl">{{ stats.total_gate_keepers }}</div>
                    <div class="text-base font-semibold text-text-secondary uppercase tracking-wider">Gate Keepers</div>
                </div>
                <div class="glass-card-premium text-center p-8">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-purple to-cyber-purple-bright rounded-xl flex items-center justify-center">
                        <span class="text-2xl">📜</span>
                    </div>
                    <div class="metric-value text-cyber-purple mb-3 font-orbitron font-bold text-4xl">{{ stats.active_proposals }}</div>
                    <div class="text-base font-semibold text-text-secondary uppercase tracking-wider">Active Proposals</div>
                </div>
                <div class="glass-card-premium text-center p-8">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-blue to-cyber-blue-bright rounded-xl flex items-center justify-center">
                        <span class="text-2xl">🔍</span>
                    </div>
                    <div class="metric-value text-cyber-blue mb-3 font-orbitron font-bold text-4xl">{{ stats.pending_verifications }}</div>
                    <div class="text-base font-semibold text-text-secondary uppercase tracking-wider">Pending Verifications</div>
                </div>
                <div class="glass-card-premium text-center p-8">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-green to-green-400 rounded-xl flex items-center justify-center">
                        <span class="text-2xl">📊</span>
                    </div>
                    <div class="metric-value text-cyber-green mb-3 font-orbitron font-bold text-4xl">{{ stats.total_voice_scrolls }}</div>
                    <div class="text-base font-semibold text-text-secondary uppercase tracking-wider">Total Voice Scrolls</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Council of 12 Gate Keepers -->
    <div class="py-16 relative bg-gradient-to-r from-cyber-cyan/10 to-transparent">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-orbitron font-bold text-cyber-cyan mb-4">
                    Council of 12 Gate Keepers
                </h2>
                <p class="text-lg text-text-secondary mb-8">
                    The sacred council responsible for verifying Israelite covenant identities
                </p>
                <a href="{{ url_for('governance.council_overview') }}" 
                   class="glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                    📋 View Full Council Details
                </a>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8">
                {% for gk in gate_keepers[:12] %}
                <div class="glass-card p-6 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="text-4xl mb-3">⚖️</div>
                    <h3 class="font-orbitron font-bold text-cyber-cyan mb-2">{{ gk.name }}</h3>
                    <p class="text-sm text-text-secondary mb-3">Gate Keeper</p>
                    {% if gk.metadata and gk.metadata.tribe_name %}
                    <p class="text-xs text-cyber-purple">Tribe of {{ gk.metadata.tribe_name }}</p>
                    {% endif %}
                    <div class="mt-3 text-xs text-cyber-green">
                        ✅ Active Council Member
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Active Voice Scrolls -->
    {% if active_scrolls %}
    <div class="py-16 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-orbitron font-bold text-cyber-purple mb-4">
                    Active Voice Scrolls
                </h2>
                <p class="text-lg text-text-secondary mb-8">
                    Current governance proposals requiring community input
                </p>
                <a href="{{ url_for('governance.voice_scrolls') }}" 
                   class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105"
                   style="border: 2px solid var(--cyber-purple);">
                    📜 View All Voice Scrolls
                </a>
            </div>
            
            <div class="space-y-6">
                {% for scroll in active_scrolls[:5] %}
                <div class="glass-card p-8 hover:scale-105 transition-all duration-300">
                    <div class="flex items-start justify-between mb-6">
                        <div class="flex-1">
                            <h3 class="text-xl font-orbitron font-bold text-white mb-2">
                                <a href="{{ url_for('governance.verification_details', proposal_id=scroll.scroll_id) }}"
                                   class="hover:text-cyber-purple transition-colors duration-300">
                                    {{ scroll.title }}
                                </a>
                            </h3>
                            <p class="text-sm text-gray-400 uppercase tracking-wider mb-3">{{ scroll.category }}</p>
                            <p class="text-text-secondary">{{ scroll.description[:200] }}...</p>
                        </div>
                        <span class="badge-success ml-4">
                            {{ scroll.status|title }}
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between text-sm text-text-tertiary">
                        <span>Created: {{ scroll.created_at|format_timestamp }}</span>
                        <span>Expires: {{ scroll.expires_at|format_timestamp }}</span>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Pending Identity Verifications -->
    {% if pending_verifications %}
    <div class="py-16 relative bg-gradient-to-r from-cyber-blue/10 to-transparent">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-orbitron font-bold text-cyber-blue mb-4">
                    Pending Identity Verifications
                </h2>
                <p class="text-lg text-text-secondary mb-8">
                    Israelite identity registrations awaiting Gate Keeper verification
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for verification in pending_verifications[:6] %}
                <div class="glass-card p-6 hover:scale-105 transition-all duration-300">
                    <div class="text-center mb-4">
                        <div class="text-3xl mb-2">🔍</div>
                        <h3 class="font-orbitron font-bold text-cyber-blue mb-2">
                            {{ verification.applicant_data.get('name', 'Anonymous') }}
                        </h3>
                        <p class="text-sm text-text-secondary">Tribe of {{ verification.tribe_code }}</p>
                    </div>
                    
                    <div class="space-y-2 text-xs text-text-tertiary">
                        <div class="flex justify-between">
                            <span>Votes Cast:</span>
                            <span>{{ verification.votes|length }}/12</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Status:</span>
                            <span class="text-cyber-blue">{{ verification.status }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Submitted:</span>
                            <span>{{ verification.created_at|format_timestamp }}</span>
                        </div>
                    </div>
                    
                    <div class="mt-4 text-center">
                        <a href="{{ url_for('governance.verification_details', proposal_id=verification.proposal_id) }}"
                           class="text-cyber-blue hover:text-cyber-cyan transition-colors text-sm font-semibold">
                            View Details →
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Governance Process Overview -->
    <div class="py-20 relative">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="glass-card-premium p-12">
                <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-6">
                    Biblical Governance Process
                </h2>
                <p class="text-lg text-text-secondary mb-8 leading-relaxed">
                    Our governance follows biblical principles with transparent voting, 
                    Gate Keeper verification for covenant identities, and community participation 
                    in all major decisions affecting the covenant community.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ url_for('governance.governance_process') }}"
                       class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-bold text-lg transition-all duration-300 hover:scale-105">
                        📖 Learn the Process
                    </a>
                    <a href="{{ url_for('governance.governance_faq') }}"
                       class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold text-lg transition-all duration-300 hover:scale-105">
                        ❓ Governance FAQ
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
