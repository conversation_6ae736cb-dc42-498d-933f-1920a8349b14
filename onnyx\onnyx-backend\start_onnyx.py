#!/usr/bin/env python3
"""
ONNYX Platform Startup Script

This script starts the complete ONNYX platform:
- Flask web application
- P2P blockchain network
- Mining network
- Database initialization

Usage:
    python start_onnyx.py
"""

import os
import sys
import logging
import asyncio
import threading
import time
from pathlib import Path

# Add the current directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('onnyx_startup.log')
    ]
)
logger = logging.getLogger("onnyx.startup")

def initialize_database():
    """Initialize the database if needed."""
    try:
        from init_db import check_tables, init_database
        logger.info("🗄️ Checking database...")
        
        if not check_tables():
            logger.info("📋 Initializing database tables...")
            init_database()
            logger.info("✅ Database initialized successfully")
        else:
            logger.info("✅ Database tables already exist")
        return True
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        return False

def start_blockchain_network():
    """Start the blockchain network in a background thread."""
    def run_blockchain():
        try:
            logger.info("🔗 Starting blockchain network...")
            
            # Import blockchain components
            from scripts.deploy_p2p_network import NetworkDeployment
            from scripts.deploy_p2p_mining import P2PMiningDeployment
            
            async def deploy_blockchain():
                # Start P2P network
                logger.info("📡 Deploying P2P network...")
                network = NetworkDeployment()
                network_success = await network.deploy_network()
                
                if network_success:
                    logger.info("✅ P2P network deployed successfully")
                    
                    # Start mining network
                    logger.info("⛏️ Deploying mining network...")
                    mining = P2PMiningDeployment()
                    mining_success = await mining.deploy_mining_network()
                    
                    if mining_success:
                        logger.info("✅ Mining network deployed successfully")
                    else:
                        logger.warning("⚠️ Mining network deployment failed")
                    
                    # Keep running
                    logger.info("🌐 Blockchain network is running...")
                    while True:
                        await asyncio.sleep(60)
                else:
                    logger.error("❌ P2P network deployment failed")
            
            # Run blockchain in new event loop
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(deploy_blockchain())
            
        except Exception as e:
            logger.error(f"Blockchain startup error: {e}")
    
    # Start blockchain in background thread
    blockchain_thread = threading.Thread(target=run_blockchain, daemon=True)
    blockchain_thread.start()
    logger.info("🚀 Blockchain network startup initiated")
    
    # Give blockchain time to start
    time.sleep(3)

def start_web_application():
    """Start the Flask web application."""
    try:
        logger.info("🌐 Starting web application...")
        from web.app import create_app
        
        app = create_app()
        
        # Set blockchain status flags
        app.blockchain_running = True
        app.mining_running = True
        
        logger.info("🎉 ONNYX Platform is ready!")
        logger.info("📍 Web interface: http://localhost:5000")
        logger.info("🔐 Admin access: Create account via Eden Mode")
        logger.info("🛑 Press Ctrl+C to stop")
        
        # Start Flask app
        app.run(
            debug=False,  # Set to False for production
            host='0.0.0.0',
            port=5000,
            threaded=True
        )
        
    except Exception as e:
        logger.error(f"❌ Web application startup failed: {e}")
        return False

def main():
    """Main startup sequence."""
    logger.info("🔥 STARTING ONNYX PLATFORM")
    logger.info("=" * 50)
    
    # Step 1: Initialize database
    if not initialize_database():
        logger.error("❌ Startup failed: Database initialization error")
        return 1
    
    # Step 2: Start blockchain network
    start_blockchain_network()
    
    # Step 3: Start web application
    try:
        start_web_application()
    except KeyboardInterrupt:
        logger.info("🛑 Shutdown requested by user")
    except Exception as e:
        logger.error(f"❌ Startup error: {e}")
        return 1
    
    logger.info("👋 ONNYX Platform stopped")
    return 0

if __name__ == '__main__':
    sys.exit(main())
