{% extends "base.html" %}

{% block title %}Manual Onboarding Dashboard - ONNYX{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900">
    <!-- Header -->
    <section class="pt-24 pb-16">
        <div class="container-xl mx-auto px-6">
            <div class="text-center mb-16">
                <h1 class="text-5xl md:text-6xl font-orbitron font-bold text-white mb-6">
                    🛡️ Manual Onboarding
                </h1>
                <p class="text-xl text-text-secondary max-w-3xl mx-auto">
                    Streamlined process for adding citizens, validators, and tribal elders to the ONNYX covenant blockchain
                </p>
            </div>
        </div>
    </section>

    <!-- Platform Statistics -->
    <section class="pb-16">
        <div class="container-xl mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                <div class="glass-card-premium text-center p-8">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-cyan to-cyan-400 rounded-xl flex items-center justify-center">
                        <span class="text-2xl">👤</span>
                    </div>
                    <div class="text-4xl font-orbitron font-bold text-cyber-cyan mb-2">{{ stats.identities }}</div>
                    <div class="text-text-secondary">Total Identities</div>
                </div>
                <div class="glass-card-premium text-center p-8">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-purple to-purple-400 rounded-xl flex items-center justify-center">
                        <span class="text-2xl">🏢</span>
                    </div>
                    <div class="text-4xl font-orbitron font-bold text-cyber-purple mb-2">{{ stats.selas }}</div>
                    <div class="text-text-secondary">Active Validators</div>
                </div>
                <div class="glass-card-premium text-center p-8">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-green to-green-400 rounded-xl flex items-center justify-center">
                        <span class="text-2xl">👑</span>
                    </div>
                    <div class="text-4xl font-orbitron font-bold text-cyber-green mb-2">{{ stats.tribal_elders }}</div>
                    <div class="text-text-secondary">Tribal Elders</div>
                </div>
                <div class="glass-card-premium text-center p-8">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-blue to-blue-400 rounded-xl flex items-center justify-center">
                        <span class="text-2xl">🌟</span>
                    </div>
                    <div class="text-4xl font-orbitron font-bold text-cyber-blue mb-2">{{ stats.citizens }}</div>
                    <div class="text-text-secondary">Citizens</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Onboarding Actions -->
    <section class="pb-16">
        <div class="container-xl mx-auto px-6">
            <h2 class="text-3xl font-orbitron font-bold text-white mb-8 text-center">Add New Participants</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                <a href="{{ url_for('onboarding.add_citizen') }}" class="glass-card-premium p-8 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-cyber-cyan to-cyan-400 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <span class="text-3xl">👤</span>
                    </div>
                    <h3 class="text-xl font-orbitron font-bold text-cyber-cyan mb-3">Add Citizen</h3>
                    <p class="text-text-secondary">Register a new citizen with covenant identity</p>
                </a>
                
                <a href="{{ url_for('onboarding.add_validator') }}" class="glass-card-premium p-8 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-cyber-purple to-purple-400 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <span class="text-3xl">🏢</span>
                    </div>
                    <h3 class="text-xl font-orbitron font-bold text-cyber-purple mb-3">Add Validator</h3>
                    <p class="text-text-secondary">Register a business validator (Sela) with mining tier</p>
                </a>
                
                <a href="{{ url_for('onboarding.add_tribal_elder') }}" class="glass-card-premium p-8 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-cyber-green to-green-400 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <span class="text-3xl">👑</span>
                    </div>
                    <h3 class="text-xl font-orbitron font-bold text-cyber-green mb-3">Add Tribal Elder</h3>
                    <p class="text-text-secondary">Register a tribal elder with governance privileges</p>
                </a>
                
                <a href="{{ url_for('onboarding.bulk_import') }}" class="glass-card-premium p-8 text-center hover:scale-105 transition-all duration-300 group">
                    <div class="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-cyber-blue to-blue-400 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                        <span class="text-3xl">📊</span>
                    </div>
                    <h3 class="text-xl font-orbitron font-bold text-cyber-blue mb-3">Bulk Import</h3>
                    <p class="text-text-secondary">Import multiple users from CSV or JSON</p>
                </a>
            </div>
        </div>
    </section>

    <!-- Recent Activity -->
    <section class="pb-16">
        <div class="container-xl mx-auto px-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Recent Identities -->
                <div class="glass-card-premium p-8">
                    <h3 class="text-2xl font-orbitron font-bold text-white mb-6">Recent Identities</h3>
                    <div class="space-y-4">
                        {% for identity in recent_identities %}
                        <div class="flex items-center justify-between p-4 bg-dark-800/50 rounded-lg">
                            <div>
                                <div class="font-semibold text-white">{{ identity.name }}</div>
                                <div class="text-sm text-text-secondary">{{ identity.identity_id }}</div>
                                <div class="text-xs text-cyber-cyan">{{ identity.role.title() }}{% if identity.covenant_tribe %} - {{ identity.covenant_tribe }}{% endif %}</div>
                            </div>
                            <div class="text-xs text-text-secondary">
                                {{ moment(identity.created_at).fromNow() }}
                            </div>
                        </div>
                        {% else %}
                        <div class="text-center text-text-secondary py-8">
                            No identities registered yet
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Recent Validators -->
                <div class="glass-card-premium p-8">
                    <h3 class="text-2xl font-orbitron font-bold text-white mb-6">Recent Validators</h3>
                    <div class="space-y-4">
                        {% for sela in recent_selas %}
                        <div class="flex items-center justify-between p-4 bg-dark-800/50 rounded-lg">
                            <div>
                                <div class="font-semibold text-white">{{ sela.business_name }}</div>
                                <div class="text-sm text-text-secondary">{{ sela.sela_id }}</div>
                                <div class="text-xs text-cyber-purple">{{ sela.mining_tier.title() }} Tier</div>
                            </div>
                            <div class="text-xs text-text-secondary">
                                {{ moment(sela.created_at).fromNow() }}
                            </div>
                        </div>
                        {% else %}
                        <div class="text-center text-text-secondary py-8">
                            No validators registered yet
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
// Add moment.js for time formatting if not already included
if (typeof moment === 'undefined') {
    window.moment = function(timestamp) {
        const date = new Date(timestamp * 1000);
        return {
            fromNow: function() {
                const now = new Date();
                const diff = now - date;
                const minutes = Math.floor(diff / 60000);
                const hours = Math.floor(diff / 3600000);
                const days = Math.floor(diff / 86400000);
                
                if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
                if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
                if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
                return 'Just now';
            }
        };
    };
}
</script>
{% endblock %}
