#!/usr/bin/env python3
"""
ONNYX Database User Creation Test
This tests user creation directly through the database layer
"""

import sqlite3
import os
import sys
from datetime import datetime

def main():
    print("🔍 ONNYX User Creation Tests")
    print("=" * 50)
    
    # Database path
    db_path = r"c:\Users\<USER>\Documents\dev\onnyx\shared\db\db\onnyx.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        print("✅ Database connection successful")
        
        # Check tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📊 Found {len(tables)} tables")
        
        # Check identities table structure
        cursor.execute("PRAGMA table_info(identities);")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        print(f"👤 Identities table columns: {', '.join(column_names)}")
        
        print("\n🔹 Creating Test Users")
        
        # Test data for Israelite user
        israelite_data = {
            'name': 'Test Israelite User',
            'public_key': 'onnyx_test_israelite_key_12345abcdef',
            'role_class': 'israelite',
            'tribal_affiliation': 'judah',
            'verification_level': 1,
            'covenant_accepted': True,
            'vault_status': 'active',
            'etzem_score': 100,
            'status': 'active',
            'email': '<EMAIL>',
            'protection_tier': 1,
            'zeman_count': 0,
            'cipp_tier': 1,
            'full_name': 'Test Israelite User',
            'tribe_name': 'Judah',
            'labor_category': 'priest',
            'eden_mode_completed': True,
            'sabbath_observer': True,
            'deeds_score': 50,
            'timezone': 'UTC'
        }
        
        # Test data for Witness Nation user
        witness_data = {
            'name': 'Test Witness Nation User',
            'public_key': 'onnyx_test_witness_key_67890fedcba',
            'role_class': 'witness_nation',
            'nation_code': 'USA',
            'nation_name': 'United States',
            'verification_level': 1,
            'covenant_accepted': True,
            'vault_status': 'active',
            'etzem_score': 80,
            'nation_of_origin': 'United States',
            'status': 'active',
            'email': '<EMAIL>',
            'protection_tier': 1,
            'zeman_count': 0,
            'cipp_tier': 1,
            'full_name': 'Test Witness Nation User',
            'labor_category': 'craftsman',
            'eden_mode_completed': True,
            'sabbath_observer': False,
            'deeds_score': 40,
            'timezone': 'UTC'
        }
        
        # Function to insert user
        def insert_user(data, user_type):
            try:
                # Build dynamic INSERT query based on available columns
                valid_columns = [col for col in data.keys() if col in column_names]
                placeholders = ', '.join(['?' for _ in valid_columns])
                columns_str = ', '.join(valid_columns)
                values = [data[col] for col in valid_columns]
                
                query = f"INSERT INTO identities ({columns_str}) VALUES ({placeholders})"
                cursor.execute(query, values)
                
                user_id = cursor.lastrowid
                print(f"✅ Created {user_type} user with ID: {user_id}")
                return user_id
                
            except Exception as e:
                print(f"❌ Failed to create {user_type} user: {e}")
                return None
        
        # Insert test users
        israelite_id = insert_user(israelite_data, "Israelite")
        witness_id = insert_user(witness_data, "Witness Nation")
        
        # Commit changes
        conn.commit()
        
        # Verify users were created
        print("\n🔍 Verifying Created Users:")
        cursor.execute("SELECT identity_id, name, role_class, email FROM identities WHERE name LIKE 'Test%'")
        test_users = cursor.fetchall()
        
        for user in test_users:
            print(f"👤 User ID: {user[0]}, Name: {user[1]}, Role: {user[2]}, Email: {user[3]}")
        
        print(f"\n✅ Test completed successfully! Created {len(test_users)} test users.")
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("ONNYX Database User Creation Test")
    print("This tests user creation directly through the database layer")
    print("=" * 70)
    main()
